<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>莫比乌斯环 3D 可视化 (流畅炫酷版)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #e0e0e0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 动态星空背景 */
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s ease-in-out infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #5b86e5, #36d1dc, #5b86e5);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: flowingGradient 4s ease-in-out infinite;
            font-weight: 300;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        }

        @keyframes flowingGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        main {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            margin-bottom: 30px;
        }

        #scene-container {
            position: relative;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 20px;
            box-shadow: 
                0 0 40px rgba(0, 212, 255, 0.2),
                inset 0 0 40px rgba(0, 0, 0, 0.5);
            overflow: hidden;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        #mobius-canvas {
            width: 100%;
            height: 600px;
            display: block;
            border-radius: 20px;
        }

        .controls-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 
                0 0 30px rgba(0, 212, 255, 0.3),
                inset 0 0 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.4);
            min-width: 220px;
        }

        .controls-panel h3 {
            margin-bottom: 20px;
            color: #fff;
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .control-group {
            margin-bottom: 18px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #00d4ff;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
            font-size: 0.95rem;
        }

        .control-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
            accent-color: #00d4ff;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-top: 8px;
            height: 8px;
            border-radius: 4px;
            background: linear-gradient(90deg, #00d4ff, #5b86e5);
            outline: none;
            -webkit-appearance: none;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: radial-gradient(circle, #fff 30%, #00d4ff 100%);
            cursor: pointer;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
            border: 2px solid #fff;
        }

        .control-group button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #00d4ff, #5b86e5);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .control-group button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.6);
            background: linear-gradient(45deg, #5b86e5, #36d1dc);
        }

        .info-panel {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 
                0 0 40px rgba(0, 212, 255, 0.2),
                inset 0 0 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            height: fit-content;
        }

        .info-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }

        .info-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-section h3 {
            color: #fff;
            margin-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
        }

        .info-section p {
            color: #b0d4ff;
            line-height: 1.8;
            margin-bottom: 12px;
            text-shadow: 0 0 5px rgba(176, 212, 255, 0.3);
        }

        .info-section ul {
            color: #b0d4ff;
            padding-left: 20px;
        }

        .info-section li {
            margin-bottom: 10px;
            line-height: 1.7;
            text-shadow: 0 0 5px rgba(176, 212, 255, 0.3);
        }

        .info-section li strong {
            color: #fff;
            font-weight: 600;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.8);
        }

        footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            padding: 30px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .performance-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff88;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 13px;
            font-family: 'Courier New', monospace;
            border: 1px solid #00ff88;
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
            text-shadow: 0 0 5px rgba(0, 255, 136, 0.8);
        }

        .mode-indicator {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0, 0, 0, 0.8);
            color: #00d4ff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            border: 1px solid rgba(0, 212, 255, 0.5);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        @media (max-width: 1024px) {
            main {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .controls-panel {
                position: static;
                margin-top: 20px;
                width: 100%;
            }
            
            #scene-container {
                height: 500px;
            }
            
            #mobius-canvas {
                height: 500px;
            }
        }

        @media (max-width: 768px) {
            header h1 {
                font-size: 2.5rem;
            }
            
            #scene-container {
                height: 400px;
            }
            
            #mobius-canvas {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <!-- 动态星空背景 -->
    <div class="stars" id="stars"></div>
    
    <div class="container">
        <!-- 头部标题 -->
        <header>
            <h1>莫比乌斯环 3D 可视化</h1>
            <p class="subtitle">🚀 流畅炫酷版 | 动态特效 | 多种模式 | 60FPS流畅体验</p>
        </header>

        <!-- 主要内容区域 -->
        <main>
            <!-- 3D 场景容器 -->
            <div id="scene-container">
                <canvas id="mobius-canvas" width="800" height="600"></canvas>
                
                <!-- 模式指示器 -->
                <div class="mode-indicator" id="mode-indicator">
                    模式: 标准
                </div>
                
                <!-- 性能监控 -->
                <div class="performance-info" id="performance-info">
                    ⚡ FPS: -- | 渲染: --ms | 模式: --
                </div>
                
                <!-- 控制面板 -->
                <div class="controls-panel">
                    <h3>🎮 炫酷控制台</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="auto-rotate" checked>
                            🔄 自动旋转
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="show-particle" checked>
                            🔴 轨迹演示
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-lighting" checked>
                            💡 动态光照
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-glow" checked>
                            ✨ 发光效果
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-morphing" checked>
                            🌊 形态变换
                        </label>
                    </div>
                    <div class="control-group">
                        <label>🚀 旋转速度:</label>
                        <input type="range" id="rotation-speed" min="0.1" max="4" step="0.1" value="1.2">
                    </div>
                    <div class="control-group">
                        <label>🎯 网格密度:</label>
                        <input type="range" id="grid-density" min="30" max="100" step="5" value="60">
                    </div>
                    <div class="control-group">
                        <label>🌈 色彩模式:</label>
                        <select id="color-mode" style="width: 100%; padding: 5px; border-radius: 4px; background: rgba(0,0,0,0.5); color: #00d4ff; border: 1px solid #00d4ff;">
                            <option value="cool">冷色调</option>
                            <option value="warm">暖色调</option>
                            <option value="rainbow">彩虹模式</option>
                            <option value="neon">霓虹模式</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <button id="reset-view">🔄 重置视角</button>
                    </div>
                </div>
            </div>

            <!-- 教育信息面板 -->
            <div class="info-panel">
                <div class="info-section">
                    <h3>🚀 流畅炫酷特性</h3>
                    <ul>
                        <li><strong>动态星空</strong>：100颗闪烁星星背景</li>
                        <li><strong>形态变换</strong>：实时几何形状变化</li>
                        <li><strong>多色彩模式</strong>：冷/暖/彩虹/霓虹四种模式</li>
                        <li><strong>智能优化</strong>：60FPS稳定流畅体验</li>
                        <li><strong>动态光效</strong>：自适应发光和阴影</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>🎯 什么是莫比乌斯环？</h3>
                    <p>莫比乌斯环是一个具有奇特性质的几何体，它只有一个面和一个边界。这个流畅炫酷版本通过动态形态变换和多彩光效，生动展现了它的神奇数学特性！</p>
                </div>

                <div class="info-section">
                    <h3>🌈 色彩模式说明</h3>
                    <ul>
                        <li><strong>冷色调</strong>：蓝青色系，科技感十足</li>
                        <li><strong>暖色调</strong>：红橙色系，温暖舒适</li>
                        <li><strong>彩虹模式</strong>：全光谱色彩，绚丽多彩</li>
                        <li><strong>霓虹模式</strong>：荧光色系，夜店风格</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>🎮 交互说明</h3>
                    <ul>
                        <li><strong>旋转</strong>：按住鼠标左键拖拽</li>
                        <li><strong>缩放</strong>：使用鼠标滚轮</li>
                        <li><strong>轨迹演示</strong>：观察彩色小球的动态轨迹</li>
                        <li><strong>形态变换</strong>：实时几何形状波动效果</li>
                        <li><strong>网格密度</strong>：调整几何精度(30-100)</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>⌨️ 快捷键</h3>
                    <ul>
                        <li><strong>空格键</strong>：切换自动旋转</li>
                        <li><strong>G键</strong>：切换发光效果</li>
                        <li><strong>M键</strong>：切换形态变换</li>
                        <li><strong>R键</strong>：重置视角</li>
                        <li><strong>1-4键</strong>：切换色彩模式</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>⚡ 性能特性</h3>
                    <ul>
                        <li><strong>帧率控制</strong>：智能60FPS限制</li>
                        <li><strong>动态优化</strong>：根据复杂度自动调整</li>
                        <li><strong>内存管理</strong>：高效缓存和对象复用</li>
                        <li><strong>GPU加速</strong>：硬件加速渲染</li>
                    </ul>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer>
            <p>🚀 流畅炫酷Canvas 2D版本 | 动态特效数学可视化 | 多模式体验</p>
        </footer>
    </div>

    <script>
        /**
         * 流畅炫酷莫比乌斯环可视化
         * 特色功能：
         * 1. 动态星空背景
         * 2. 多种色彩模式
         * 3. 形态变换效果
         * 4. 智能性能优化
         * 5. 流畅60FPS体验
         */

        // 动态星空背景
        class StarField {
            constructor() {
                this.container = document.getElementById('stars');
                this.stars = [];
                this.maxStars = 100;
                this.init();
            }

            init() {
                for (let i = 0; i < this.maxStars; i++) {
                    this.createStar();
                }
            }

            createStar() {
                const star = document.createElement('div');
                star.className = 'star';

                const size = Math.random() * 3 + 1;
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                const duration = Math.random() * 3 + 2;
                const delay = Math.random() * 3;

                star.style.width = size + 'px';
                star.style.height = size + 'px';
                star.style.left = x + 'px';
                star.style.top = y + 'px';
                star.style.animationDuration = duration + 's';
                star.style.animationDelay = delay + 's';

                this.container.appendChild(star);
                this.stars.push(star);
            }
        }

        // 增强性能监控
        class SmoothPerformanceMonitor {
            constructor() {
                this.frameCount = 0;
                this.lastTime = performance.now();
                this.fps = 0;
                this.renderTime = 0;
                this.mode = 'Standard';
                this.updateInterval = 30;
            }

            startFrame() {
                this.frameStart = performance.now();
            }

            endFrame(mode = 'Standard') {
                this.frameCount++;
                this.renderTime = performance.now() - this.frameStart;
                this.mode = mode;

                if (this.frameCount % this.updateInterval === 0) {
                    const currentTime = performance.now();
                    this.fps = Math.round(1000 * this.updateInterval / (currentTime - this.lastTime));
                    this.lastTime = currentTime;
                    this.updateDisplay();
                }
            }

            updateDisplay() {
                const perfInfo = document.getElementById('performance-info');
                if (perfInfo) {
                    perfInfo.innerHTML = `⚡ FPS: <span style="color: #00ff88">${this.fps}</span> | 渲染: <span style="color: #ff6b6b">${this.renderTime.toFixed(1)}ms</span> | 模式: <span style="color: #00d4ff">${this.mode}</span>`;
                }
            }
        }

        // 色彩系统
        class ColorSystem {
            static modes = {
                cool: {
                    primary: [0, 212, 255],
                    secondary: [91, 134, 229],
                    accent: [54, 209, 220]
                },
                warm: {
                    primary: [255, 107, 107],
                    secondary: [255, 159, 67],
                    accent: [255, 206, 84]
                },
                rainbow: {
                    primary: [255, 0, 150],
                    secondary: [0, 255, 150],
                    accent: [150, 0, 255]
                },
                neon: {
                    primary: [57, 255, 20],
                    secondary: [255, 20, 147],
                    accent: [20, 255, 255]
                }
            };

            static getColor(mode, t, lightness = 0.7) {
                const colors = this.modes[mode] || this.modes.cool;
                const phase = (t * Math.PI * 2) % (Math.PI * 2);

                let r, g, b;
                if (phase < Math.PI * 2 / 3) {
                    const ratio = phase / (Math.PI * 2 / 3);
                    r = colors.primary[0] * (1 - ratio) + colors.secondary[0] * ratio;
                    g = colors.primary[1] * (1 - ratio) + colors.secondary[1] * ratio;
                    b = colors.primary[2] * (1 - ratio) + colors.secondary[2] * ratio;
                } else if (phase < Math.PI * 4 / 3) {
                    const ratio = (phase - Math.PI * 2 / 3) / (Math.PI * 2 / 3);
                    r = colors.secondary[0] * (1 - ratio) + colors.accent[0] * ratio;
                    g = colors.secondary[1] * (1 - ratio) + colors.accent[1] * ratio;
                    b = colors.secondary[2] * (1 - ratio) + colors.accent[2] * ratio;
                } else {
                    const ratio = (phase - Math.PI * 4 / 3) / (Math.PI * 2 / 3);
                    r = colors.accent[0] * (1 - ratio) + colors.primary[0] * ratio;
                    g = colors.accent[1] * (1 - ratio) + colors.primary[1] * ratio;
                    b = colors.accent[2] * (1 - ratio) + colors.primary[2] * ratio;
                }

                r = Math.floor(r * lightness);
                g = Math.floor(g * lightness);
                b = Math.floor(b * lightness);

                return `rgb(${r}, ${g}, ${b})`;
            }
        }

        // 优化的3D数学工具
        class SmoothMath3D {
            static sinCache = new Map();
            static cosCache = new Map();

            static sin(angle) {
                const key = Math.round(angle * 1000);
                if (!this.sinCache.has(key)) {
                    this.sinCache.set(key, Math.sin(angle));
                }
                return this.sinCache.get(key);
            }

            static cos(angle) {
                const key = Math.round(angle * 1000);
                if (!this.cosCache.has(key)) {
                    this.cosCache.set(key, Math.cos(angle));
                }
                return this.cosCache.get(key);
            }

            static createRotationMatrix(rx, ry, rz) {
                const cx = this.cos(rx), sx = this.sin(rx);
                const cy = this.cos(ry), sy = this.sin(ry);
                const cz = this.cos(rz), sz = this.sin(rz);

                return {
                    m11: cy * cz, m12: -cy * sz, m13: sy,
                    m21: sx * sy * cz + cx * sz, m22: -sx * sy * sz + cx * cz, m23: -sx * cy,
                    m31: -cx * sy * cz + sx * sz, m32: cx * sy * sz + sx * cz, m33: cx * cy
                };
            }

            static transformPoint(point, matrix) {
                return {
                    x: point.x * matrix.m11 + point.y * matrix.m12 + point.z * matrix.m13,
                    y: point.x * matrix.m21 + point.y * matrix.m22 + point.z * matrix.m23,
                    z: point.x * matrix.m31 + point.y * matrix.m32 + point.z * matrix.m33
                };
            }

            static project(point, centerX, centerY, scale, distance = 10) {
                const projScale = scale * distance / (distance + point.z);
                return {
                    x: centerX + point.x * projScale,
                    y: centerY + point.y * projScale,
                    depth: point.z,
                    scale: projScale
                };
            }
        }

        // 炫酷几何体
        class SmoothMobiusGeometry {
            constructor(uSteps = 60, vSteps = 8) {
                this.uSteps = uSteps;
                this.vSteps = vSteps;
                this.vertices = [];
                this.normals = [];
                this.generateGeometry();
            }

            // 带形态变换的莫比乌斯环
            mobiusPoint(u, v, morphTime = 0) {
                const radius = 3;
                const width = 0.7;
                const halfU = u * 0.5;

                // 形态变换效果
                const morphFactor = Math.sin(morphTime * 0.002) * 0.3;
                const wave1 = Math.sin(u * 3 + morphTime * 0.003) * 0.1 * morphFactor;
                const wave2 = Math.cos(u * 2 + morphTime * 0.002) * 0.05 * morphFactor;

                const adjustedRadius = radius + wave1;
                const adjustedWidth = width + wave2;

                const x = Math.cos(u) * (adjustedRadius + v * adjustedWidth * Math.cos(halfU));
                const y = Math.sin(u) * (adjustedRadius + v * adjustedWidth * Math.cos(halfU));
                const z = v * adjustedWidth * Math.sin(halfU) + wave2;

                return { x, y, z };
            }

            calculateNormal(u, v, morphTime = 0) {
                const epsilon = 0.01;
                const p1 = this.mobiusPoint(u, v, morphTime);
                const p2 = this.mobiusPoint(u + epsilon, v, morphTime);
                const p3 = this.mobiusPoint(u, v + epsilon, morphTime);

                const dx1 = p2.x - p1.x, dy1 = p2.y - p1.y, dz1 = p2.z - p1.z;
                const dx2 = p3.x - p1.x, dy2 = p3.y - p1.y, dz2 = p3.z - p1.z;

                const nx = dy1 * dz2 - dz1 * dy2;
                const ny = dz1 * dx2 - dx1 * dz2;
                const nz = dx1 * dy2 - dy1 * dx2;

                const length = Math.sqrt(nx * nx + ny * ny + nz * nz);
                return length > 0 ? { x: nx / length, y: ny / length, z: nz / length } : { x: 0, y: 0, z: 1 };
            }

            generateGeometry() {
                this.vertices = [];
                this.normals = [];

                for (let i = 0; i <= this.uSteps; i++) {
                    for (let j = 0; j <= this.vSteps; j++) {
                        const u = (i / this.uSteps) * Math.PI * 2;
                        const v = ((j / this.vSteps) - 0.5);

                        this.vertices.push({
                            u: i, v: j,
                            uParam: u, vParam: v
                        });
                    }
                }
            }

            updateDensity(uSteps, vSteps) {
                uSteps = Math.min(uSteps, 100);
                vSteps = Math.min(vSteps, 12);

                if (this.uSteps !== uSteps || this.vSteps !== vSteps) {
                    this.uSteps = uSteps;
                    this.vSteps = vSteps;
                    this.generateGeometry();
                    return true;
                }
                return false;
            }
        }

        // 主渲染器
        class SmoothCoolMobiusRenderer {
            constructor() {
                this.canvas = document.getElementById('mobius-canvas');
                this.ctx = this.canvas.getContext('2d');

                // 性能监控
                this.perfMonitor = new SmoothPerformanceMonitor();

                // 基础参数
                this.centerX = 400;
                this.centerY = 300;
                this.scale = 110;
                this.rotationX = 0.3;
                this.rotationY = 0;
                this.rotationZ = 0;
                this.autoRotate = true;
                this.showParticle = true;
                this.enableLighting = true;
                this.enableGlow = true;
                this.enableMorphing = true;
                this.rotationSpeed = 1.2;
                this.colorMode = 'cool';
                this.particleT = 0;

                // 几何体
                this.geometry = new SmoothMobiusGeometry(60, 8);

                // 缓存
                this.projectedPoints = [];
                this.sortedIndices = [];

                // 鼠标交互
                this.isDragging = false;
                this.lastMouseX = 0;
                this.lastMouseY = 0;

                // 光照参数
                this.lightDirection = { x: 0.5, y: 0.5, z: 1 };
                this.ambientLight = 0.4;
                this.diffuseLight = 0.6;

                // 动画时间
                this.time = 0;

                // 帧率控制
                this.targetFPS = 60;
                this.frameInterval = 1000 / this.targetFPS;
                this.lastFrameTime = 0;

                this.init();
                this.setupEvents();
                this.animate();
            }

            init() {
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());

                this.projectedPoints = new Array(this.geometry.vertices.length);
                this.sortedIndices = new Array(this.geometry.vertices.length);
                for (let i = 0; i < this.sortedIndices.length; i++) {
                    this.sortedIndices[i] = i;
                }
            }

            resizeCanvas() {
                const container = this.canvas.parentElement;
                const newWidth = container.clientWidth;
                const newHeight = Math.min(600, container.clientHeight);

                if (this.canvas.width !== newWidth || this.canvas.height !== newHeight) {
                    this.canvas.width = newWidth;
                    this.canvas.height = newHeight;
                    this.centerX = newWidth / 2;
                    this.centerY = newHeight / 2;
                }
            }

            // 主渲染管道
            render(currentTime) {
                // 帧率控制
                if (currentTime - this.lastFrameTime < this.frameInterval) {
                    return;
                }
                this.lastFrameTime = currentTime;

                this.perfMonitor.startFrame();
                this.time = currentTime;

                // 动态背景
                this.createDynamicBackground();

                // 计算旋转矩阵
                const rotMatrix = SmoothMath3D.createRotationMatrix(
                    this.rotationX + Math.sin(this.time * 0.0008) * 0.05,
                    this.rotationY,
                    this.rotationZ + Math.cos(this.time * 0.001) * 0.03
                );

                // 批量投影
                this.projectAllVertices(rotMatrix);

                // 深度排序
                this.sortVerticesByDepth();

                // 渲染莫比乌斯环
                this.drawSmoothMobiusRing();

                // 绘制轨迹小球
                if (this.showParticle) {
                    this.drawSmoothParticle(rotMatrix);
                }

                // 更新模式指示器
                this.updateModeIndicator();

                this.perfMonitor.endFrame(this.getCurrentMode());
            }

            createDynamicBackground() {
                // 动态渐变背景
                const gradient = this.ctx.createRadialGradient(
                    this.centerX, this.centerY, 0,
                    this.centerX, this.centerY, Math.max(this.canvas.width, this.canvas.height) / 2
                );

                const t = this.time * 0.0005;
                const color1 = ColorSystem.getColor(this.colorMode, t, 0.1);
                const color2 = ColorSystem.getColor(this.colorMode, t + 0.3, 0.05);
                const color3 = ColorSystem.getColor(this.colorMode, t + 0.6, 0.02);

                gradient.addColorStop(0, color1);
                gradient.addColorStop(0.5, color2);
                gradient.addColorStop(1, color3);

                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            }

            projectAllVertices(rotMatrix) {
                const vertices = this.geometry.vertices;

                for (let i = 0; i < vertices.length; i++) {
                    const vertex = vertices[i];

                    // 动态计算顶点位置（支持形态变换）
                    const morphTime = this.enableMorphing ? this.time : 0;
                    const point3D = this.geometry.mobiusPoint(vertex.uParam, vertex.vParam, morphTime);
                    const normal = this.geometry.calculateNormal(vertex.uParam, vertex.vParam, morphTime);

                    const rotatedVertex = SmoothMath3D.transformPoint(point3D, rotMatrix);
                    const rotatedNormal = SmoothMath3D.transformPoint(normal, rotMatrix);

                    const projected = SmoothMath3D.project(rotatedVertex, this.centerX, this.centerY, this.scale);

                    let lightIntensity = this.ambientLight;
                    if (this.enableLighting) {
                        const dotProduct = Math.max(0,
                            rotatedNormal.x * this.lightDirection.x +
                            rotatedNormal.y * this.lightDirection.y +
                            rotatedNormal.z * this.lightDirection.z
                        );
                        lightIntensity += this.diffuseLight * dotProduct;
                    }

                    const colorT = vertex.uParam / (Math.PI * 2) + this.time * 0.0003;

                    this.projectedPoints[i] = {
                        x: projected.x,
                        y: projected.y,
                        depth: projected.depth,
                        scale: projected.scale,
                        u: vertex.u,
                        v: vertex.v,
                        uParam: vertex.uParam,
                        vParam: vertex.vParam,
                        lightIntensity: Math.min(1, lightIntensity),
                        color: ColorSystem.getColor(this.colorMode, colorT, lightIntensity)
                    };
                }
            }

            sortVerticesByDepth() {
                this.sortedIndices.sort((a, b) => this.projectedPoints[b].depth - this.projectedPoints[a].depth);
            }

            drawSmoothMobiusRing() {
                const points = this.projectedPoints;
                const uSteps = this.geometry.uSteps;
                const vSteps = this.geometry.vSteps;

                // 动态网格线
                this.ctx.lineWidth = 2;

                if (this.enableGlow) {
                    this.ctx.shadowBlur = 12;
                }

                // U方向的线
                for (let i = 0; i <= uSteps; i += 3) {
                    const t = (i / uSteps) + this.time * 0.0005;
                    this.ctx.strokeStyle = ColorSystem.getColor(this.colorMode, t, 0.8);
                    this.ctx.shadowColor = this.ctx.strokeStyle;

                    this.ctx.beginPath();
                    let hasValidPoint = false;

                    for (let j = 0; j <= vSteps; j++) {
                        const idx = i * (vSteps + 1) + j;
                        if (idx < points.length) {
                            const point = points[idx];
                            if (!hasValidPoint) {
                                this.ctx.moveTo(point.x, point.y);
                                hasValidPoint = true;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    if (hasValidPoint) this.ctx.stroke();
                }

                // V方向的线
                for (let j = 0; j <= vSteps; j += 2) {
                    const t = (j / vSteps) + this.time * 0.0008;
                    this.ctx.strokeStyle = ColorSystem.getColor(this.colorMode, t, 0.7);
                    this.ctx.shadowColor = this.ctx.strokeStyle;

                    this.ctx.beginPath();
                    let hasValidPoint = false;

                    for (let i = 0; i <= uSteps; i++) {
                        const idx = i * (vSteps + 1) + j;
                        if (idx < points.length) {
                            const point = points[idx];
                            if (!hasValidPoint) {
                                this.ctx.moveTo(point.x, point.y);
                                hasValidPoint = true;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    if (hasValidPoint) this.ctx.stroke();
                }

                // 绘制发光点
                this.ctx.shadowBlur = this.enableGlow ? 15 : 0;

                for (let i = 0; i < this.sortedIndices.length; i++) {
                    const idx = this.sortedIndices[i];
                    const point = points[idx];

                    if (point && point.scale > 0.1) {
                        const radius = Math.max(2, 4 * point.scale / this.scale);

                        // 主体
                        this.ctx.fillStyle = point.color;
                        this.ctx.shadowColor = point.color;
                        this.ctx.beginPath();
                        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
                        this.ctx.fill();

                        // 外层光晕
                        if (this.enableGlow && i % 3 === 0) {
                            this.ctx.fillStyle = point.color + '40';
                            this.ctx.beginPath();
                            this.ctx.arc(point.x, point.y, radius * 1.8, 0, Math.PI * 2);
                            this.ctx.fill();
                        }
                    }
                }

                this.ctx.shadowBlur = 0;
            }

            drawSmoothParticle(rotMatrix) {
                const u = this.particleT * Math.PI * 2;
                const v = Math.sin(this.time * 0.001) * 0.4;

                const morphTime = this.enableMorphing ? this.time : 0;
                const point3D = this.geometry.mobiusPoint(u, v, morphTime);
                const rotatedPoint = SmoothMath3D.transformPoint(point3D, rotMatrix);
                const point2D = SmoothMath3D.project(rotatedPoint, this.centerX, this.centerY, this.scale);

                const radius = 8;
                const colorT = this.time * 0.003;
                const particleColor = ColorSystem.getColor(this.colorMode, colorT, 1);

                // 外层光晕
                if (this.enableGlow) {
                    const glowGradient = this.ctx.createRadialGradient(
                        point2D.x, point2D.y, 0,
                        point2D.x, point2D.y, radius * 3
                    );
                    glowGradient.addColorStop(0, particleColor);
                    glowGradient.addColorStop(1, 'transparent');

                    this.ctx.fillStyle = glowGradient;
                    this.ctx.beginPath();
                    this.ctx.arc(point2D.x, point2D.y, radius * 3, 0, Math.PI * 2);
                    this.ctx.fill();
                }

                // 主体
                const mainGradient = this.ctx.createRadialGradient(
                    point2D.x - 2, point2D.y - 2, 0,
                    point2D.x, point2D.y, radius
                );
                mainGradient.addColorStop(0, '#ffffff');
                mainGradient.addColorStop(0.3, particleColor);
                mainGradient.addColorStop(1, ColorSystem.getColor(this.colorMode, colorT + 0.5, 0.5));

                this.ctx.fillStyle = mainGradient;
                this.ctx.beginPath();
                this.ctx.arc(point2D.x, point2D.y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 高光
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                this.ctx.beginPath();
                this.ctx.arc(point2D.x - 2, point2D.y - 2, 3, 0, Math.PI * 2);
                this.ctx.fill();

                this.particleT += 0.006;
                if (this.particleT > 2) {
                    this.particleT = 0;
                }
            }

            getCurrentMode() {
                let mode = this.colorMode.charAt(0).toUpperCase() + this.colorMode.slice(1);
                if (this.enableMorphing) mode += '+Morph';
                if (this.enableGlow) mode += '+Glow';
                return mode;
            }

            updateModeIndicator() {
                const indicator = document.getElementById('mode-indicator');
                if (indicator) {
                    indicator.textContent = `模式: ${this.getCurrentMode()}`;
                }
            }

            setupEvents() {
                // 鼠标事件
                const mouseHandler = {
                    handleMouseDown: (e) => {
                        this.isDragging = true;
                        this.lastMouseX = e.clientX;
                        this.lastMouseY = e.clientY;
                        e.preventDefault();
                    },

                    handleMouseMove: (e) => {
                        if (this.isDragging) {
                            const deltaX = e.clientX - this.lastMouseX;
                            const deltaY = e.clientY - this.lastMouseY;

                            this.rotationY += deltaX * 0.008;
                            this.rotationX += deltaY * 0.008;

                            this.lastMouseX = e.clientX;
                            this.lastMouseY = e.clientY;
                        }
                    },

                    handleMouseUp: () => {
                        this.isDragging = false;
                    },

                    handleWheel: (e) => {
                        e.preventDefault();
                        const scaleFactor = 1 - e.deltaY * 0.001;
                        this.scale *= scaleFactor;
                        this.scale = Math.max(40, Math.min(300, this.scale));
                    }
                };

                this.canvas.addEventListener('mousedown', mouseHandler.handleMouseDown);
                this.canvas.addEventListener('mousemove', mouseHandler.handleMouseMove);
                this.canvas.addEventListener('mouseup', mouseHandler.handleMouseUp);
                this.canvas.addEventListener('wheel', mouseHandler.handleWheel);

                // 控制面板事件
                document.getElementById('auto-rotate').addEventListener('change', (e) => {
                    this.autoRotate = e.target.checked;
                });

                document.getElementById('show-particle').addEventListener('change', (e) => {
                    this.showParticle = e.target.checked;
                });

                document.getElementById('enable-lighting').addEventListener('change', (e) => {
                    this.enableLighting = e.target.checked;
                });

                document.getElementById('enable-glow').addEventListener('change', (e) => {
                    this.enableGlow = e.target.checked;
                });

                document.getElementById('enable-morphing').addEventListener('change', (e) => {
                    this.enableMorphing = e.target.checked;
                });

                document.getElementById('rotation-speed').addEventListener('input', (e) => {
                    this.rotationSpeed = parseFloat(e.target.value);
                });

                document.getElementById('grid-density').addEventListener('input', (e) => {
                    const density = parseInt(e.target.value);
                    if (this.geometry.updateDensity(density, Math.max(6, Math.floor(density / 10)))) {
                        this.projectedPoints = new Array(this.geometry.vertices.length);
                        this.sortedIndices = new Array(this.geometry.vertices.length);
                        for (let i = 0; i < this.sortedIndices.length; i++) {
                            this.sortedIndices[i] = i;
                        }
                    }
                });

                document.getElementById('color-mode').addEventListener('change', (e) => {
                    this.colorMode = e.target.value;
                });

                document.getElementById('reset-view').addEventListener('click', () => {
                    this.rotationX = 0.3;
                    this.rotationY = 0;
                    this.rotationZ = 0;
                    this.scale = 110;
                });
            }

            animate(currentTime = performance.now()) {
                if (this.autoRotate) {
                    this.rotationY += 0.004 * this.rotationSpeed;
                }

                this.render(currentTime);
                requestAnimationFrame((time) => this.animate(time));
            }
        }

        // 初始化流畅炫酷版本
        document.addEventListener('DOMContentLoaded', () => {
            try {
                // 初始化星空背景
                new StarField();

                // 初始化主渲染器
                window.smoothCoolMobius = new SmoothCoolMobiusRenderer();

                console.log('🚀 流畅炫酷莫比乌斯环初始化成功！');

                // 添加键盘快捷键
                document.addEventListener('keydown', (e) => {
                    switch(e.key.toLowerCase()) {
                        case ' ':
                            e.preventDefault();
                            document.getElementById('auto-rotate').click();
                            break;
                        case 'g':
                            document.getElementById('enable-glow').click();
                            break;
                        case 'm':
                            document.getElementById('enable-morphing').click();
                            break;
                        case 'r':
                            document.getElementById('reset-view').click();
                            break;
                        case '1':
                            document.getElementById('color-mode').value = 'cool';
                            document.getElementById('color-mode').dispatchEvent(new Event('change'));
                            break;
                        case '2':
                            document.getElementById('color-mode').value = 'warm';
                            document.getElementById('color-mode').dispatchEvent(new Event('change'));
                            break;
                        case '3':
                            document.getElementById('color-mode').value = 'rainbow';
                            document.getElementById('color-mode').dispatchEvent(new Event('change'));
                            break;
                        case '4':
                            document.getElementById('color-mode').value = 'neon';
                            document.getElementById('color-mode').dispatchEvent(new Event('change'));
                            break;
                    }
                });

            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('scene-container').innerHTML =
                    '<div style="color: #00d4ff; text-align: center; padding: 50px;">💥 初始化失败，请检查浏览器支持</div>';
            }
        });
    </script>
</body>
</html>
