<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>莫比乌斯环 3D 可视化 (平衡版)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #e0e0e0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #74b9ff, #0984e3, #6c5ce7);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gentleGradient 6s ease-in-out infinite;
            font-weight: 300;
        }

        @keyframes gentleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
            color: #b0c4de;
        }

        main {
            display: grid;
            grid-template-columns: 1fr 380px;
            gap: 30px;
            margin-bottom: 30px;
        }

        #scene-container {
            position: relative;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        #mobius-canvas {
            width: 100%;
            height: 600px;
            display: block;
            border-radius: 15px;
        }

        .controls-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.6);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            min-width: 200px;
        }

        .controls-panel h3 {
            margin-bottom: 15px;
            color: #fff;
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #b0c4de;
            font-size: 0.9rem;
        }

        .control-group input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.1);
            accent-color: #74b9ff;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-top: 5px;
            height: 6px;
            border-radius: 3px;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            outline: none;
            -webkit-appearance: none;
        }

        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.5);
            border: 2px solid #74b9ff;
        }

        .control-group button {
            width: 100%;
            padding: 10px;
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .control-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: fit-content;
        }

        .info-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-section h3 {
            color: #fff;
            margin-bottom: 12px;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .info-section p {
            color: #b0c4de;
            line-height: 1.6;
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .info-section ul {
            color: #b0c4de;
            padding-left: 18px;
        }

        .info-section li {
            margin-bottom: 8px;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .info-section li strong {
            color: #fff;
            font-weight: 600;
        }

        footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            padding: 20px;
        }

        .performance-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #74b9ff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(116, 185, 255, 0.3);
        }

        @media (max-width: 1024px) {
            main {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .controls-panel {
                position: static;
                margin-top: 20px;
                width: 100%;
            }
            
            #scene-container {
                height: 500px;
            }
            
            #mobius-canvas {
                height: 500px;
            }
        }

        @media (max-width: 768px) {
            header h1 {
                font-size: 2.2rem;
            }
            
            #scene-container {
                height: 400px;
            }
            
            #mobius-canvas {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <header>
            <h1>莫比乌斯环 3D 可视化</h1>
            <p class="subtitle">🎯 平衡版本 | 优雅视觉 | 流畅性能 | 舒适体验</p>
        </header>

        <!-- 主要内容区域 -->
        <main>
            <!-- 3D 场景容器 -->
            <div id="scene-container">
                <canvas id="mobius-canvas" width="800" height="600"></canvas>
                
                <!-- 性能监控 -->
                <div class="performance-info" id="performance-info">
                    FPS: -- | 渲染: --ms
                </div>
                
                <!-- 控制面板 -->
                <div class="controls-panel">
                    <h3>🎛️ 控制面板</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="auto-rotate" checked>
                            🔄 自动旋转
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="show-particle" checked>
                            🔴 轨迹演示
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-lighting" checked>
                            💡 光照效果
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="enable-glow" checked>
                            ✨ 柔和光效
                        </label>
                    </div>
                    <div class="control-group">
                        <label>旋转速度:</label>
                        <input type="range" id="rotation-speed" min="0.1" max="3" step="0.1" value="1">
                    </div>
                    <div class="control-group">
                        <label>网格密度:</label>
                        <input type="range" id="grid-density" min="30" max="80" step="5" value="50">
                    </div>
                    <div class="control-group">
                        <button id="reset-view">🔄 重置视角</button>
                    </div>
                </div>
            </div>

            <!-- 教育信息面板 -->
            <div class="info-panel">
                <div class="info-section">
                    <h3>🎯 平衡版本特性</h3>
                    <ul>
                        <li><strong>优雅配色</strong>：柔和的蓝色调，护眼舒适</li>
                        <li><strong>流畅性能</strong>：优化渲染，降低GPU负载</li>
                        <li><strong>平衡特效</strong>：适度的光效，不过度炫目</li>
                        <li><strong>响应式设计</strong>：完美适配各种设备</li>
                    </ul>
                </div>
                
                <div class="info-section">
                    <h3>🎯 什么是莫比乌斯环？</h3>
                    <p>莫比乌斯环是一个具有奇特性质的几何体，它只有一个面和一个边界。如果你沿着它的表面走，你会发现你能够到达"另一面"而不需要跨越边界。</p>
                </div>
                
                <div class="info-section">
                    <h3>🔬 数学特性</h3>
                    <ul>
                        <li><strong>单面性</strong>：只有一个连续的表面</li>
                        <li><strong>单边界</strong>：只有一条连续的边界线</li>
                        <li><strong>不可定向</strong>：没有明确的"内侧"和"外侧"</li>
                        <li><strong>拓扑性质</strong>：欧拉示性数为0</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>🎮 交互说明</h3>
                    <ul>
                        <li><strong>旋转</strong>：按住鼠标左键拖拽</li>
                        <li><strong>缩放</strong>：使用鼠标滚轮</li>
                        <li><strong>轨迹演示</strong>：观察小球沿表面运动</li>
                        <li><strong>网格密度</strong>：调整几何精度(30-80)</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>⚡ 性能优化</h3>
                    <ul>
                        <li><strong>智能缓存</strong>：减少重复计算</li>
                        <li><strong>批量渲染</strong>：优化绘制流程</li>
                        <li><strong>自适应质量</strong>：根据设备性能调整</li>
                        <li><strong>内存管理</strong>：高效的对象复用</li>
                    </ul>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer>
            <p>🎯 平衡版Canvas 2D | 优雅数学可视化 | 流畅用户体验</p>
        </footer>
    </div>

    <script>
        /**
         * 平衡版莫比乌斯环可视化
         * 优化重点：
         * 1. 降低颜色饱和度，使用柔和配色
         * 2. 减少特效复杂度，提升性能
         * 3. 优化渲染频率和计算量
         * 4. 平衡视觉效果和流畅度
         */

        // 轻量级性能监控
        class BalancedPerformanceMonitor {
            constructor() {
                this.frameCount = 0;
                this.lastTime = performance.now();
                this.fps = 0;
                this.renderTime = 0;
                this.updateInterval = 60; // 降低更新频率
            }

            startFrame() {
                this.frameStart = performance.now();
            }

            endFrame() {
                this.frameCount++;
                this.renderTime = performance.now() - this.frameStart;

                if (this.frameCount % this.updateInterval === 0) {
                    const currentTime = performance.now();
                    this.fps = Math.round(1000 * this.updateInterval / (currentTime - this.lastTime));
                    this.lastTime = currentTime;
                    this.updateDisplay();
                }
            }

            updateDisplay() {
                const perfInfo = document.getElementById('performance-info');
                if (perfInfo) {
                    perfInfo.textContent = `FPS: ${this.fps} | 渲染: ${this.renderTime.toFixed(1)}ms`;
                }
            }
        }

        // 优化的3D数学工具
        class BalancedMath3D {
            // 简化的缓存系统
            static sinCache = new Map();
            static cosCache = new Map();
            static cacheSize = 0;
            static maxCacheSize = 1000; // 限制缓存大小

            static sin(angle) {
                const key = Math.round(angle * 1000); // 降低精度以减少缓存
                if (!this.sinCache.has(key)) {
                    if (this.cacheSize >= this.maxCacheSize) {
                        this.sinCache.clear();
                        this.cacheSize = 0;
                    }
                    this.sinCache.set(key, Math.sin(angle));
                    this.cacheSize++;
                }
                return this.sinCache.get(key);
            }

            static cos(angle) {
                const key = Math.round(angle * 1000);
                if (!this.cosCache.has(key)) {
                    if (this.cacheSize >= this.maxCacheSize) {
                        this.cosCache.clear();
                        this.cacheSize = 0;
                    }
                    this.cosCache.set(key, Math.cos(angle));
                    this.cacheSize++;
                }
                return this.cosCache.get(key);
            }

            static createRotationMatrix(rx, ry, rz) {
                const cx = this.cos(rx), sx = this.sin(rx);
                const cy = this.cos(ry), sy = this.sin(ry);
                const cz = this.cos(rz), sz = this.sin(rz);

                return {
                    m11: cy * cz, m12: -cy * sz, m13: sy,
                    m21: sx * sy * cz + cx * sz, m22: -sx * sy * sz + cx * cz, m23: -sx * cy,
                    m31: -cx * sy * cz + sx * sz, m32: cx * sy * sz + sx * cz, m33: cx * cy
                };
            }

            static transformPoint(point, matrix) {
                return {
                    x: point.x * matrix.m11 + point.y * matrix.m12 + point.z * matrix.m13,
                    y: point.x * matrix.m21 + point.y * matrix.m22 + point.z * matrix.m23,
                    z: point.x * matrix.m31 + point.y * matrix.m32 + point.z * matrix.m33
                };
            }

            static project(point, centerX, centerY, scale, distance = 10) {
                const projScale = scale * distance / (distance + point.z);
                return {
                    x: centerX + point.x * projScale,
                    y: centerY + point.y * projScale,
                    depth: point.z,
                    scale: projScale
                };
            }

            // 柔和的颜色生成 - 降低饱和度
            static gentleColor(t, lightness = 0.6) {
                const hue = ((t * 0.3) % 1) * 240 + 200; // 限制在蓝紫色范围
                const saturation = 40; // 大幅降低饱和度
                const light = Math.max(30, Math.min(80, lightness * 100));
                return `hsl(${hue}, ${saturation}%, ${light}%)`;
            }
        }

        // 简化的几何体
        class BalancedMobiusGeometry {
            constructor(uSteps = 50, vSteps = 6) { // 降低默认密度
                this.uSteps = uSteps;
                this.vSteps = vSteps;
                this.vertices = [];
                this.normals = [];
                this.generateGeometry();
            }

            mobiusPoint(u, v) {
                const radius = 3;
                const width = 0.6;
                const halfU = u * 0.5;

                const x = Math.cos(u) * (radius + v * width * Math.cos(halfU));
                const y = Math.sin(u) * (radius + v * width * Math.cos(halfU));
                const z = v * width * Math.sin(halfU);

                return { x, y, z };
            }

            calculateNormal(u, v) {
                const epsilon = 0.02; // 稍微增大以减少计算精度
                const p1 = this.mobiusPoint(u, v);
                const p2 = this.mobiusPoint(u + epsilon, v);
                const p3 = this.mobiusPoint(u, v + epsilon);

                const dx1 = p2.x - p1.x, dy1 = p2.y - p1.y, dz1 = p2.z - p1.z;
                const dx2 = p3.x - p1.x, dy2 = p3.y - p1.y, dz2 = p3.z - p1.z;

                const nx = dy1 * dz2 - dz1 * dy2;
                const ny = dz1 * dx2 - dx1 * dz2;
                const nz = dx1 * dy2 - dy1 * dx2;

                const length = Math.sqrt(nx * nx + ny * ny + nz * nz);
                return length > 0 ? { x: nx / length, y: ny / length, z: nz / length } : { x: 0, y: 0, z: 1 };
            }

            generateGeometry() {
                this.vertices = [];
                this.normals = [];

                for (let i = 0; i <= this.uSteps; i++) {
                    for (let j = 0; j <= this.vSteps; j++) {
                        const u = (i / this.uSteps) * Math.PI * 2;
                        const v = ((j / this.vSteps) - 0.5);

                        const vertex = this.mobiusPoint(u, v);
                        const normal = this.calculateNormal(u, v);

                        this.vertices.push({
                            ...vertex,
                            u: i, v: j,
                            uParam: u, vParam: v
                        });
                        this.normals.push(normal);
                    }
                }
            }

            updateDensity(uSteps, vSteps) {
                // 限制最大密度以保证性能
                uSteps = Math.min(uSteps, 80);
                vSteps = Math.min(vSteps, 10);

                if (this.uSteps !== uSteps || this.vSteps !== vSteps) {
                    this.uSteps = uSteps;
                    this.vSteps = vSteps;
                    this.generateGeometry();
                    return true;
                }
                return false;
            }
        }

        // 平衡版主渲染器
        class BalancedMobiusRenderer {
            constructor() {
                this.canvas = document.getElementById('mobius-canvas');
                this.ctx = this.canvas.getContext('2d');

                // 性能监控
                this.perfMonitor = new BalancedPerformanceMonitor();

                // 基础参数
                this.centerX = 400;
                this.centerY = 300;
                this.scale = 100;
                this.rotationX = 0.2;
                this.rotationY = 0;
                this.rotationZ = 0;
                this.autoRotate = true;
                this.showParticle = true;
                this.enableLighting = true;
                this.enableGlow = true;
                this.rotationSpeed = 1;
                this.particleT = 0;

                // 几何体 - 使用较低密度
                this.geometry = new BalancedMobiusGeometry(50, 6);

                // 缓存
                this.projectedPoints = [];
                this.sortedIndices = [];

                // 鼠标交互
                this.isDragging = false;
                this.lastMouseX = 0;
                this.lastMouseY = 0;

                // 光照参数 - 柔和设置
                this.lightDirection = { x: 0.4, y: 0.4, z: 0.8 };
                this.ambientLight = 0.5;
                this.diffuseLight = 0.5;

                // 帧率控制
                this.targetFPS = 60;
                this.frameInterval = 1000 / this.targetFPS;
                this.lastFrameTime = 0;

                this.init();
                this.setupEvents();
                this.animate();
            }

            init() {
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());

                // 预分配数组
                this.projectedPoints = new Array(this.geometry.vertices.length);
                this.sortedIndices = new Array(this.geometry.vertices.length);
                for (let i = 0; i < this.sortedIndices.length; i++) {
                    this.sortedIndices[i] = i;
                }
            }

            resizeCanvas() {
                const container = this.canvas.parentElement;
                const newWidth = container.clientWidth;
                const newHeight = Math.min(600, container.clientHeight);

                if (this.canvas.width !== newWidth || this.canvas.height !== newHeight) {
                    this.canvas.width = newWidth;
                    this.canvas.height = newHeight;
                    this.centerX = newWidth / 2;
                    this.centerY = newHeight / 2;
                }
            }

            // 优化的渲染管道
            render(currentTime) {
                // 帧率控制
                if (currentTime - this.lastFrameTime < this.frameInterval) {
                    return;
                }
                this.lastFrameTime = currentTime;

                this.perfMonitor.startFrame();

                // 柔和的背景
                this.createGentleBackground();

                // 计算旋转矩阵
                const rotMatrix = BalancedMath3D.createRotationMatrix(
                    this.rotationX,
                    this.rotationY,
                    this.rotationZ
                );

                // 批量投影
                this.projectAllVertices(rotMatrix);

                // 深度排序
                this.sortVerticesByDepth();

                // 渲染莫比乌斯环
                this.drawBalancedMobiusRing();

                // 绘制轨迹小球
                if (this.showParticle) {
                    this.drawBalancedParticle(rotMatrix);
                }

                this.perfMonitor.endFrame();
            }

            createGentleBackground() {
                // 简单的渐变背景
                const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
                gradient.addColorStop(0, '#2c3e50');
                gradient.addColorStop(0.5, '#34495e');
                gradient.addColorStop(1, '#2c3e50');

                this.ctx.fillStyle = gradient;
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            }

            projectAllVertices(rotMatrix) {
                const vertices = this.geometry.vertices;
                const normals = this.geometry.normals;

                for (let i = 0; i < vertices.length; i++) {
                    const vertex = vertices[i];
                    const normal = normals[i];

                    const rotatedVertex = BalancedMath3D.transformPoint(vertex, rotMatrix);
                    const rotatedNormal = BalancedMath3D.transformPoint(normal, rotMatrix);

                    const projected = BalancedMath3D.project(rotatedVertex, this.centerX, this.centerY, this.scale);

                    let lightIntensity = this.ambientLight;
                    if (this.enableLighting) {
                        const dotProduct = Math.max(0,
                            rotatedNormal.x * this.lightDirection.x +
                            rotatedNormal.y * this.lightDirection.y +
                            rotatedNormal.z * this.lightDirection.z
                        );
                        lightIntensity += this.diffuseLight * dotProduct;
                    }

                    this.projectedPoints[i] = {
                        x: projected.x,
                        y: projected.y,
                        depth: projected.depth,
                        scale: projected.scale,
                        u: vertex.u,
                        v: vertex.v,
                        uParam: vertex.uParam,
                        vParam: vertex.vParam,
                        lightIntensity: Math.min(1, lightIntensity),
                        color: this.getBalancedColor(vertex.uParam, lightIntensity)
                    };
                }
            }

            sortVerticesByDepth() {
                // 使用更高效的排序
                this.sortedIndices.sort((a, b) => this.projectedPoints[b].depth - this.projectedPoints[a].depth);
            }

            getBalancedColor(u, lightIntensity) {
                const t = u / (Math.PI * 2);
                return BalancedMath3D.gentleColor(t, lightIntensity);
            }

            drawBalancedMobiusRing() {
                const points = this.projectedPoints;
                const uSteps = this.geometry.uSteps;
                const vSteps = this.geometry.vSteps;

                // 绘制柔和的网格线
                this.ctx.lineWidth = 1.5;
                this.ctx.strokeStyle = '#74b9ff';

                if (this.enableGlow) {
                    this.ctx.shadowBlur = 8;
                    this.ctx.shadowColor = '#74b9ff';
                } else {
                    this.ctx.shadowBlur = 0;
                }

                // U方向的线 - 减少绘制频率
                for (let i = 0; i <= uSteps; i += 4) {
                    this.ctx.beginPath();
                    let hasValidPoint = false;

                    for (let j = 0; j <= vSteps; j++) {
                        const idx = i * (vSteps + 1) + j;
                        if (idx < points.length) {
                            const point = points[idx];
                            if (!hasValidPoint) {
                                this.ctx.moveTo(point.x, point.y);
                                hasValidPoint = true;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    if (hasValidPoint) this.ctx.stroke();
                }

                // V方向的线
                for (let j = 0; j <= vSteps; j += 2) {
                    this.ctx.beginPath();
                    let hasValidPoint = false;

                    for (let i = 0; i <= uSteps; i++) {
                        const idx = i * (vSteps + 1) + j;
                        if (idx < points.length) {
                            const point = points[idx];
                            if (!hasValidPoint) {
                                this.ctx.moveTo(point.x, point.y);
                                hasValidPoint = true;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    if (hasValidPoint) this.ctx.stroke();
                }

                // 绘制表面点 - 简化版本
                this.ctx.shadowBlur = this.enableGlow ? 6 : 0;

                for (let i = 0; i < this.sortedIndices.length; i += 2) { // 跳过一些点以提升性能
                    const idx = this.sortedIndices[i];
                    const point = points[idx];

                    if (point && point.scale > 0.2) {
                        const radius = Math.max(1.5, 3 * point.scale / this.scale);

                        this.ctx.fillStyle = point.color;
                        this.ctx.shadowColor = point.color;
                        this.ctx.beginPath();
                        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
                        this.ctx.fill();
                    }
                }

                this.ctx.shadowBlur = 0;
            }

            drawBalancedParticle(rotMatrix) {
                const u = this.particleT * Math.PI * 2;
                const v = 0;

                const point3D = this.geometry.mobiusPoint(u, v);
                const rotatedPoint = BalancedMath3D.transformPoint(point3D, rotMatrix);
                const point2D = BalancedMath3D.project(rotatedPoint, this.centerX, this.centerY, this.scale);

                // 柔和的粒子
                const radius = 6;

                // 外层光晕
                if (this.enableGlow) {
                    const glowGradient = this.ctx.createRadialGradient(
                        point2D.x, point2D.y, 0,
                        point2D.x, point2D.y, radius * 2
                    );
                    glowGradient.addColorStop(0, '#74b9ff');
                    glowGradient.addColorStop(1, 'transparent');

                    this.ctx.fillStyle = glowGradient;
                    this.ctx.beginPath();
                    this.ctx.arc(point2D.x, point2D.y, radius * 2, 0, Math.PI * 2);
                    this.ctx.fill();
                }

                // 主体
                const mainGradient = this.ctx.createRadialGradient(
                    point2D.x - 2, point2D.y - 2, 0,
                    point2D.x, point2D.y, radius
                );
                mainGradient.addColorStop(0, '#ffffff');
                mainGradient.addColorStop(0.4, '#74b9ff');
                mainGradient.addColorStop(1, '#0984e3');

                this.ctx.fillStyle = mainGradient;
                this.ctx.beginPath();
                this.ctx.arc(point2D.x, point2D.y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // 高光
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                this.ctx.beginPath();
                this.ctx.arc(point2D.x - 2, point2D.y - 2, 2, 0, Math.PI * 2);
                this.ctx.fill();

                this.particleT += 0.008;
                if (this.particleT > 2) {
                    this.particleT = 0;
                }
            }

            setupEvents() {
                // 优化的事件处理
                const mouseHandler = {
                    handleMouseDown: (e) => {
                        this.isDragging = true;
                        this.lastMouseX = e.clientX;
                        this.lastMouseY = e.clientY;
                        e.preventDefault();
                    },

                    handleMouseMove: (e) => {
                        if (this.isDragging) {
                            const deltaX = e.clientX - this.lastMouseX;
                            const deltaY = e.clientY - this.lastMouseY;

                            this.rotationY += deltaX * 0.008;
                            this.rotationX += deltaY * 0.008;

                            this.lastMouseX = e.clientX;
                            this.lastMouseY = e.clientY;
                        }
                    },

                    handleMouseUp: () => {
                        this.isDragging = false;
                    },

                    handleWheel: (e) => {
                        e.preventDefault();
                        const scaleFactor = 1 - e.deltaY * 0.001;
                        this.scale *= scaleFactor;
                        this.scale = Math.max(30, Math.min(250, this.scale));
                    }
                };

                this.canvas.addEventListener('mousedown', mouseHandler.handleMouseDown);
                this.canvas.addEventListener('mousemove', mouseHandler.handleMouseMove);
                this.canvas.addEventListener('mouseup', mouseHandler.handleMouseUp);
                this.canvas.addEventListener('wheel', mouseHandler.handleWheel);

                // 控制面板事件
                document.getElementById('auto-rotate').addEventListener('change', (e) => {
                    this.autoRotate = e.target.checked;
                });

                document.getElementById('show-particle').addEventListener('change', (e) => {
                    this.showParticle = e.target.checked;
                });

                document.getElementById('enable-lighting').addEventListener('change', (e) => {
                    this.enableLighting = e.target.checked;
                });

                document.getElementById('enable-glow').addEventListener('change', (e) => {
                    this.enableGlow = e.target.checked;
                });

                document.getElementById('rotation-speed').addEventListener('input', (e) => {
                    this.rotationSpeed = parseFloat(e.target.value);
                });

                document.getElementById('grid-density').addEventListener('input', (e) => {
                    const density = parseInt(e.target.value);
                    if (this.geometry.updateDensity(density, Math.max(4, Math.floor(density / 10)))) {
                        this.projectedPoints = new Array(this.geometry.vertices.length);
                        this.sortedIndices = new Array(this.geometry.vertices.length);
                        for (let i = 0; i < this.sortedIndices.length; i++) {
                            this.sortedIndices[i] = i;
                        }
                    }
                });

                document.getElementById('reset-view').addEventListener('click', () => {
                    this.rotationX = 0.2;
                    this.rotationY = 0;
                    this.rotationZ = 0;
                    this.scale = 100;
                });
            }

            animate(currentTime = performance.now()) {
                if (this.autoRotate) {
                    this.rotationY += 0.004 * this.rotationSpeed;
                }

                this.render(currentTime);
                requestAnimationFrame((time) => this.animate(time));
            }
        }

        // 初始化平衡版本
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.balancedMobius = new BalancedMobiusRenderer();
                console.log('🎯 平衡版莫比乌斯环初始化成功！');
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('scene-container').innerHTML =
                    '<div style="color: #74b9ff; text-align: center; padding: 50px;">初始化失败，请检查浏览器支持</div>';
            }
        });
    </script>
</body>
</html>
