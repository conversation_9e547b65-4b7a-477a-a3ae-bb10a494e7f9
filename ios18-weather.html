<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 18 Weather</title>
    <style>
        /* iOS 18 Design System Variables */
        :root {
            /* Colors - Light Mode */
            --primary-bg: #F2F2F7;
            --card-bg: rgba(255, 255, 255, 0.8);
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --accent-blue: #007AFF;
            --accent-orange: #FF9500;
            --accent-red: #FF3B30;
            --accent-green: #34C759;
            
            /* Spacing */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            
            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
            
            /* Shadows */
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
            
            /* Blur */
            --blur-light: blur(10px);
            --blur-medium: blur(20px);
            --blur-heavy: blur(40px);
        }

        /* Dark Mode */
        body.dark-mode {
            --primary-bg: #000000;
            --card-bg: rgba(28, 28, 30, 0.8);
            --text-primary: #FFFFFF;
            --text-secondary: #8E8E93;
        }

        /* Auto Dark Mode */
        @media (prefers-color-scheme: dark) {
            body:not(.light-mode) {
                --primary-bg: #000000;
                --card-bg: rgba(28, 28, 30, 0.8);
                --text-primary: #FFFFFF;
                --text-secondary: #8E8E93;
            }
        }

        /* SF Pro Font */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            background: var(--primary-bg);
            min-height: 100vh;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }

        /* Dynamic Background */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bg-sunny {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF6B6B 100%);
        }

        .bg-windy {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
        }

        .bg-rainy {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 50%, #74b9ff 100%);
        }

        .bg-snowy {
            background: linear-gradient(135deg, #ddd6fe 0%, #a78bfa 50%, #8b5cf6 100%);
        }

        /* Main Container */
        .weather-container {
            display: flex;
            height: 100vh;
            padding: var(--spacing-lg);
            gap: var(--spacing-lg);
            align-items: center;
            justify-content: center;
            overflow-x: auto;
            scroll-behavior: smooth;
        }

        /* Weather Card */
        .weather-card {
            min-width: 320px;
            height: 480px;
            background: var(--card-bg);
            backdrop-filter: var(--blur-medium);
            -webkit-backdrop-filter: var(--blur-medium);
            border-radius: var(--radius-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-heavy);
            padding: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .weather-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
        }

        .weather-card.active {
            transform: scale(1.05);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        /* Weather Icon */
        .weather-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 80px;
            position: relative;
            transition: all 0.3s ease;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .weather-icon:hover {
            transform: rotateY(15deg) rotateX(5deg) scale(1.1);
        }

        .weather-icon.sunny {
            animation: sunRotate 20s linear infinite, sunPulse 2s ease-in-out infinite;
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.6));
            position: relative;
            z-index: 2;
        }

        .weather-icon.sunny::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 160px;
            height: 160px;
            transform: translate(-50%, -50%);
            background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, rgba(255, 165, 0, 0.1) 50%, transparent 70%);
            border-radius: 50%;
            animation: sunHalo 4s ease-in-out infinite;
            z-index: -1;
            pointer-events: none;
        }

        .weather-icon.windy {
            animation: windShake 0.3s ease-in-out infinite alternate, windSway 2s ease-in-out infinite;
            filter: drop-shadow(0 0 15px rgba(116, 185, 255, 0.5));
        }

        .weather-icon.rainy {
            animation: rainBounce 1s ease-in-out infinite, rainShimmer 2s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(135, 206, 235, 0.6));
        }

        .weather-icon.snowy {
            animation: snowFloat 3s ease-in-out infinite, snowRotate 30s linear infinite, snowSparkle 1.5s ease-in-out infinite;
            filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
        }

        /* 晴天动画 */
        @keyframes sunRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes sunPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.15); }
        }

        @keyframes sunGlow {
            0%, 100% {
                filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5));
            }
            50% {
                filter: drop-shadow(0 0 25px rgba(255, 165, 0, 0.7));
            }
        }

        @keyframes sunHalo {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.6;
            }
        }

        /* 大风动画 */
        @keyframes windShake {
            0% { transform: translateX(0) skewX(0deg); }
            100% { transform: translateX(3px) skewX(2deg); }
        }

        @keyframes windSway {
            0%, 100% { transform: rotate(-2deg); }
            50% { transform: rotate(2deg); }
        }

        /* 暴雨动画 */
        @keyframes rainBounce {
            0%, 100% {
                transform: translateY(0) scale(1);
            }
            25% {
                transform: translateY(-5px) scale(1.05);
            }
            50% {
                transform: translateY(-8px) scale(1.1);
            }
            75% {
                transform: translateY(-3px) scale(1.02);
            }
        }

        @keyframes rainShimmer {
            0%, 100% {
                filter: drop-shadow(0 0 10px rgba(135, 206, 235, 0.6)) hue-rotate(0deg);
            }
            50% {
                filter: drop-shadow(0 0 20px rgba(135, 206, 235, 0.9)) hue-rotate(10deg);
            }
        }

        /* 暴雪动画 */
        @keyframes snowFloat {
            0%, 100% { transform: translateY(0px); }
            33% { transform: translateY(-8px); }
            66% { transform: translateY(-4px); }
        }

        @keyframes snowRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes snowSparkle {
            0%, 100% {
                filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8)) brightness(1);
            }
            50% {
                filter: drop-shadow(0 0 25px rgba(255, 255, 255, 1)) brightness(1.3);
            }
        }

        /* Temperature */
        .temperature {
            font-size: 4rem;
            font-weight: 300;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            letter-spacing: -2px;
        }

        /* Weather Description */
        .weather-description {
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        /* Location */
        .location {
            font-size: 1rem;
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            font-weight: 400;
        }

        /* Weather Details */
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-top: auto;
        }

        .detail-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-sm);
            backdrop-filter: var(--blur-light);
        }

        .detail-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
        }

        .detail-value {
            font-size: 1.1rem;
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--card-bg);
            backdrop-filter: var(--blur-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .weather-container {
                flex-direction: column;
                height: auto;
                min-height: 100vh;
                overflow-x: hidden;
                overflow-y: auto;
                padding: var(--spacing-md);
            }
            
            .weather-card {
                min-width: 100%;
                height: 400px;
                margin-bottom: var(--spacing-md);
            }
            
            .temperature {
                font-size: 3rem;
            }
            
            .weather-icon {
                width: 100px;
                height: 100px;
                font-size: 60px;
            }
        }

        /* Loading Animation */
        .loading {
            opacity: 0;
            animation: fadeIn 0.8s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* Particle Effects */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        /* 特殊处理雪花容器 */
        .particles.snow-container {
            top: -100px;
            height: calc(100% + 200px);
            overflow: visible;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: fall linear infinite;
        }

        .particle.snow {
            background: rgba(255, 255, 255, 0.9);
            animation: snowfall linear infinite, sway ease-in-out infinite;
        }

        .particle.rain {
            background: rgba(135, 206, 235, 0.8);
            border-radius: 1px;
            animation: rainfall linear infinite;
        }

        @keyframes fall {
            from {
                transform: translateY(-100px);
                opacity: 1;
            }
            to {
                transform: translateY(600px);
                opacity: 0;
            }
        }

        @keyframes snowfall {
            0% {
                transform: translateY(0px);
                opacity: 0;
            }
            5% {
                opacity: 1;
            }
            95% {
                opacity: 1;
            }
            100% {
                transform: translateY(600px);
                opacity: 0;
            }
        }

        @keyframes sway {
            0% { transform: translateX(-10px); }
            25% { transform: translateX(5px); }
            50% { transform: translateX(10px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(-10px); }
        }

        @keyframes rainfall {
            from {
                transform: translateY(-100px) rotate(10deg);
                opacity: 1;
            }
            to {
                transform: translateY(600px) rotate(10deg);
                opacity: 0;
            }
        }

        @keyframes windMove {
            from {
                transform: translateX(-50px);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            to {
                transform: translateX(400px);
                opacity: 0;
            }
        }

        @keyframes rayPulse {
            0%, 100% {
                opacity: 0.4;
            }
            50% {
                opacity: 0.8;
            }
        }

        @keyframes sparkle {
            0%, 100% {
                opacity: 0;
                transform: scale(0);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes windSwirl {
            from {
                transform: rotate(0deg) scale(0.5);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            to {
                transform: rotate(360deg) scale(1.2);
                opacity: 0;
            }
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes splash {
            0% {
                transform: scale(0) translateY(0);
                opacity: 1;
            }
            50% {
                transform: scale(1.5) translateY(-10px);
                opacity: 0.8;
            }
            100% {
                transform: scale(0.5) translateY(-20px);
                opacity: 0;
            }
        }

        @keyframes snowDrift {
            from {
                transform: rotate(0deg) scale(0.8);
                opacity: 0.3;
            }
            to {
                transform: rotate(360deg) scale(1.2);
                opacity: 0;
            }
        }

        @keyframes twinkle {
            0%, 100% {
                opacity: 0.2;
                transform: scale(0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        /* Card Glow Effects */
        .weather-card.sunny {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(255, 215, 0, 0.3);
        }

        .weather-card.windy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(116, 185, 255, 0.3);
        }

        .weather-card.rainy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(99, 110, 114, 0.3);
        }

        .weather-card.snowy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(221, 214, 254, 0.3);
        }

        /* Smooth Scrolling */
        .weather-container {
            scroll-snap-type: x mandatory;
        }

        .weather-card {
            scroll-snap-align: center;
        }

        /* Enhanced Hover Effects */
        .weather-card:hover .weather-icon {
            transform: scale(1.1);
        }

        .weather-card:hover .temperature {
            color: var(--accent-blue);
        }

        /* Glass Morphism Enhancement */
        .weather-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: var(--radius-xl);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="background-container bg-sunny" id="background"></div>
    
    <button class="theme-toggle" id="themeToggle">
        🌙
    </button>

    <div class="weather-container" id="weatherContainer">
        <!-- Weather cards will be generated by JavaScript -->
    </div>

    <script>
        // Weather data - 中文本地化
        const weatherData = [
            {
                id: 'sunny',
                icon: '☀️',
                temperature: '28°',
                description: '晴天',
                location: '北京',
                humidity: '45%',
                wind: '12 km/h',
                uv: '强',
                visibility: '10 km',
                bgClass: 'bg-sunny'
            },
            {
                id: 'windy',
                icon: '💨',
                temperature: '22°',
                description: '大风',
                location: '上海',
                humidity: '60%',
                wind: '35 km/h',
                pressure: '1013 hPa',
                visibility: '8 km',
                bgClass: 'bg-windy'
            },
            {
                id: 'rainy',
                icon: '🌧️',
                temperature: '18°',
                description: '暴雨',
                location: '深圳',
                humidity: '85%',
                wind: '18 km/h',
                precipitation: '15 mm',
                visibility: '3 km',
                bgClass: 'bg-rainy'
            },
            {
                id: 'snowy',
                icon: '❄️',
                temperature: '-5°',
                description: '暴雪',
                location: '哈尔滨',
                humidity: '90%',
                wind: '45 km/h',
                snowfall: '25 cm',
                visibility: '1 km',
                bgClass: 'bg-snowy'
            }
        ];

        // DOM elements
        const weatherContainer = document.getElementById('weatherContainer');
        const background = document.getElementById('background');
        const themeToggle = document.getElementById('themeToggle');

        // Theme management
        let isDarkMode = localStorage.getItem('darkMode') === 'true' ||
                        (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches);

        function toggleTheme() {
            isDarkMode = !isDarkMode;
            localStorage.setItem('darkMode', isDarkMode);
            updateTheme();
        }

        function updateTheme() {
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
                document.body.classList.remove('light-mode');
                themeToggle.textContent = '☀️';
            } else {
                document.body.classList.add('light-mode');
                document.body.classList.remove('dark-mode');
                themeToggle.textContent = '🌙';
            }
        }

        themeToggle.addEventListener('click', toggleTheme);

        // Create weather cards
        function createWeatherCard(data) {
            const card = document.createElement('div');
            card.className = `weather-card loading ${data.id}`;
            card.innerHTML = `
                <div class="weather-icon ${data.id}">${data.icon}</div>
                <div class="temperature">${data.temperature}</div>
                <div class="weather-description">${data.description}</div>
                <div class="location">${data.location}</div>
                <div class="weather-details">
                    <div class="detail-item">
                        <div class="detail-label">湿度</div>
                        <div class="detail-value">${data.humidity}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">风速</div>
                        <div class="detail-value">${data.wind}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">${data.uv ? '紫外线指数' : data.pressure ? '气压' : data.precipitation ? '降雨量' : '降雪量'}</div>
                        <div class="detail-value">${data.uv || data.pressure || data.precipitation || data.snowfall}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">能见度</div>
                        <div class="detail-value">${data.visibility}</div>
                    </div>
                </div>
                <div class="particles" id="particles-${data.id}"></div>
            `;

            // Add click event to change background
            card.addEventListener('click', () => {
                // Smooth background transition
                background.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                background.className = `background-container ${data.bgClass}`;

                // Remove active class from all cards with animation
                document.querySelectorAll('.weather-card').forEach(c => {
                    c.classList.remove('active');
                    c.style.transform = '';
                });

                // Add active class to clicked card with enhanced animation
                card.classList.add('active');

                // Create particles based on weather type
                createParticles(data.id);

                // Haptic feedback simulation (visual feedback)
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 100);
            });

            // Add hover effects
            card.addEventListener('mouseenter', () => {
                if (!card.classList.contains('active')) {
                    card.style.transform = 'translateY(-4px) scale(1.01)';
                }
            });

            card.addEventListener('mouseleave', () => {
                if (!card.classList.contains('active')) {
                    card.style.transform = '';
                }
            });

            return card;
        }

        // Create particle effects
        function createParticles(weatherType) {
            // Clear all existing particles first
            document.querySelectorAll('.particles').forEach(container => {
                container.innerHTML = '';
            });

            const particlesContainer = document.getElementById(`particles-${weatherType}`);
            if (!particlesContainer) return;

            // 为雪花容器添加特殊类
            if (weatherType === 'snowy') {
                particlesContainer.classList.add('snow-container');
            } else {
                particlesContainer.classList.remove('snow-container');
            }

            const particleCount = weatherType === 'snowy' ? 60 : weatherType === 'rainy' ? 80 : 0;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');

                if (weatherType === 'snowy') {
                    particle.className = 'particle snow';
                    particle.style.width = particle.style.height = (Math.random() * 6 + 3) + 'px';
                    particle.style.background = `rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2})`;
                    particle.style.borderRadius = '50%';

                    // 设置下降动画
                    const fallDuration = (Math.random() * 4 + 3) + 's';
                    const swayDuration = (Math.random() * 3 + 2) + 's';

                    particle.style.animation = `snowfall ${fallDuration} linear infinite, sway ${swayDuration} ease-in-out infinite`;
                    particle.style.animationDelay = Math.random() * 2 + 's';

                } else if (weatherType === 'rainy') {
                    particle.className = 'particle rain';
                    particle.style.width = (Math.random() * 3 + 1) + 'px';
                    particle.style.height = (Math.random() * 20 + 15) + 'px';
                    particle.style.background = `linear-gradient(to bottom, rgba(135, 206, 235, ${Math.random() * 0.8 + 0.4}), rgba(100, 180, 255, ${Math.random() * 0.6 + 0.2}))`;
                    particle.style.borderRadius = '50% 50% 50% 50% / 60% 60% 40% 40%';
                    particle.style.animationDuration = (Math.random() * 0.8 + 0.4) + 's';
                    particle.style.animationDelay = Math.random() * 1 + 's';
                    particle.style.filter = 'blur(0.3px)';
                }

                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = '0px'; // 从粒子容器的顶部开始
                particlesContainer.appendChild(particle);
            }

            // Add special effects for all weather types
            if (weatherType === 'sunny') {
                addSunRays(particlesContainer);
            } else if (weatherType === 'windy') {
                addWindLines(particlesContainer);
            } else if (weatherType === 'rainy') {
                addRainRipples(particlesContainer);
            } else if (weatherType === 'snowy') {
                addSnowDrifts(particlesContainer);
            }
        }

        // Add sun rays effect
        function addSunRays(container) {
            // 主要光线 - 放在太阳图标后面
            for (let i = 0; i < 8; i++) {
                const ray = document.createElement('div');
                ray.style.position = 'absolute';
                ray.style.top = '15%';
                ray.style.left = '50%';
                ray.style.width = '2px';
                ray.style.height = '100px';
                ray.style.background = 'linear-gradient(to bottom, rgba(255, 215, 0, 0.6), rgba(255, 165, 0, 0.3), transparent)';
                ray.style.transformOrigin = 'top center';
                ray.style.transform = `translateX(-50%) rotate(${i * 45}deg)`;
                ray.style.animation = `sunRotate 25s linear infinite, rayPulse 3s ease-in-out infinite`;
                ray.style.animationDelay = `${i * 0.2}s`;
                ray.style.borderRadius = '1px';
                ray.style.zIndex = '-1';
                ray.style.pointerEvents = 'none';
                container.appendChild(ray);
            }

            // 次要光线
            for (let i = 0; i < 12; i++) {
                const smallRay = document.createElement('div');
                smallRay.style.position = 'absolute';
                smallRay.style.top = '20%';
                smallRay.style.left = '50%';
                smallRay.style.width = '1px';
                smallRay.style.height = '60px';
                smallRay.style.background = 'linear-gradient(to bottom, rgba(255, 215, 0, 0.3), transparent)';
                smallRay.style.transformOrigin = 'top center';
                smallRay.style.transform = `translateX(-50%) rotate(${i * 30}deg)`;
                smallRay.style.animation = `sunRotate 35s linear infinite`;
                smallRay.style.animationDelay = `${i * 0.1}s`;
                smallRay.style.zIndex = '-1';
                smallRay.style.pointerEvents = 'none';
                container.appendChild(smallRay);
            }

            // 光粒子 - 远离中心
            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                const angle = Math.random() * 360;
                const distance = Math.random() * 40 + 60; // 距离中心更远
                const x = 50 + Math.cos(angle * Math.PI / 180) * distance / 2;
                const y = 50 + Math.sin(angle * Math.PI / 180) * distance / 2;

                particle.style.position = 'absolute';
                particle.style.top = y + '%';
                particle.style.left = x + '%';
                particle.style.width = (Math.random() * 2 + 1) + 'px';
                particle.style.height = particle.style.width;
                particle.style.background = `rgba(255, 215, 0, ${Math.random() * 0.5 + 0.2})`;
                particle.style.borderRadius = '50%';
                particle.style.animation = `sparkle ${Math.random() * 3 + 2}s ease-in-out infinite`;
                particle.style.animationDelay = Math.random() * 3 + 's';
                particle.style.zIndex = '-1';
                particle.style.pointerEvents = 'none';
                container.appendChild(particle);
            }
        }

        // Add wind lines effect
        function addWindLines(container) {
            // 主要风线
            for (let i = 0; i < 20; i++) {
                const line = document.createElement('div');
                line.style.position = 'absolute';
                line.style.top = Math.random() * 80 + 10 + '%';
                line.style.left = '-30px';
                line.style.width = (Math.random() * 50 + 30) + 'px';
                line.style.height = (Math.random() * 2 + 1) + 'px';
                line.style.background = `linear-gradient(to right, transparent, rgba(116, 185, 255, ${Math.random() * 0.6 + 0.3}), transparent)`;
                line.style.animation = `windMove ${Math.random() * 1.5 + 0.8}s linear infinite`;
                line.style.animationDelay = Math.random() * 2 + 's';
                line.style.borderRadius = '2px';
                line.style.filter = 'blur(0.5px)';
                container.appendChild(line);
            }

            // 风旋涡效果
            for (let i = 0; i < 5; i++) {
                const swirl = document.createElement('div');
                swirl.style.position = 'absolute';
                swirl.style.top = Math.random() * 60 + 20 + '%';
                swirl.style.left = Math.random() * 60 + 20 + '%';
                swirl.style.width = (Math.random() * 20 + 10) + 'px';
                swirl.style.height = swirl.style.width;
                swirl.style.border = `1px solid rgba(116, 185, 255, ${Math.random() * 0.4 + 0.2})`;
                swirl.style.borderRadius = '50%';
                swirl.style.animation = `windSwirl ${Math.random() * 3 + 2}s linear infinite`;
                swirl.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(swirl);
            }
        }

        // Initialize the app
        function init() {
            // Add loading state to body
            document.body.style.opacity = '0';

            weatherData.forEach((data, index) => {
                const card = createWeatherCard(data);
                weatherContainer.appendChild(card);

                // Stagger the loading animation
                setTimeout(() => {
                    card.classList.remove('loading');
                    card.style.transform = 'translateY(20px)';
                    card.style.opacity = '0';

                    // Animate card entrance
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                        card.style.transform = 'translateY(0)';
                        card.style.opacity = '1';
                    }, 50);
                }, index * 150);
            });

            // Set initial theme
            updateTheme();

            // Fade in the entire app
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.8s ease';
                document.body.style.opacity = '1';
            }, 200);

            // Set first card as active
            setTimeout(() => {
                const firstCard = weatherContainer.querySelector('.weather-card');
                if (firstCard) {
                    firstCard.click();
                }

        // Add rain ripples effect
        function addRainRipples(container) {
            // 底部涟漪效果
            for (let i = 0; i < 8; i++) {
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.bottom = '10%';
                ripple.style.left = Math.random() * 80 + 10 + '%';
                ripple.style.width = '20px';
                ripple.style.height = '20px';
                ripple.style.border = `1px solid rgba(135, 206, 235, ${Math.random() * 0.4 + 0.2})`;
                ripple.style.borderRadius = '50%';
                ripple.style.animation = `ripple ${Math.random() * 2 + 1}s ease-out infinite`;
                ripple.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(ripple);
            }

            // 水滴撞击效果
            for (let i = 0; i < 12; i++) {
                const splash = document.createElement('div');
                splash.style.position = 'absolute';
                splash.style.bottom = Math.random() * 30 + 5 + '%';
                splash.style.left = Math.random() * 90 + 5 + '%';
                splash.style.width = '3px';
                splash.style.height = '3px';
                splash.style.background = `rgba(135, 206, 235, ${Math.random() * 0.6 + 0.3})`;
                splash.style.borderRadius = '50%';
                splash.style.animation = `splash ${Math.random() * 1.5 + 0.5}s ease-out infinite`;
                splash.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(splash);
            }
        }

        // Add snow drifts effect
        function addSnowDrifts(container) {
            // 雪花旋涡
            for (let i = 0; i < 6; i++) {
                const drift = document.createElement('div');
                drift.style.position = 'absolute';
                drift.style.top = Math.random() * 60 + 20 + '%';
                drift.style.left = Math.random() * 60 + 20 + '%';
                drift.style.width = (Math.random() * 30 + 20) + 'px';
                drift.style.height = drift.style.width;
                drift.style.border = `1px solid rgba(255, 255, 255, ${Math.random() * 0.3 + 0.1})`;
                drift.style.borderRadius = '50%';
                drift.style.animation = `snowDrift ${Math.random() * 4 + 3}s linear infinite`;
                drift.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(drift);
            }

            // 雪花闪烁
            for (let i = 0; i < 15; i++) {
                const twinkle = document.createElement('div');
                twinkle.style.position = 'absolute';
                twinkle.style.top = Math.random() * 80 + 10 + '%';
                twinkle.style.left = Math.random() * 80 + 10 + '%';
                twinkle.style.width = (Math.random() * 4 + 2) + 'px';
                twinkle.style.height = twinkle.style.width;
                twinkle.style.background = `rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2})`;
                twinkle.style.borderRadius = '50%';
                twinkle.style.animation = `twinkle ${Math.random() * 2 + 1}s ease-in-out infinite`;
                twinkle.style.animationDelay = Math.random() * 2 + 's';
                twinkle.style.filter = 'blur(0.5px)';
                container.appendChild(twinkle);
            }
        }
            }, 1200);
        }

        // Smooth scroll for horizontal navigation
        function setupSmoothScroll() {
            let isScrolling = false;

            weatherContainer.addEventListener('wheel', (e) => {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
                    e.preventDefault();

                    if (!isScrolling) {
                        isScrolling = true;
                        weatherContainer.scrollBy({
                            left: e.deltaX > 0 ? 340 : -340,
                            behavior: 'smooth'
                        });

                        setTimeout(() => {
                            isScrolling = false;
                        }, 500);
                    }
                }
            });
        }

        // Add keyboard navigation
        function setupKeyboardNavigation() {
            let currentIndex = 0;

            document.addEventListener('keydown', (e) => {
                const cards = document.querySelectorAll('.weather-card');

                if (e.key === 'ArrowLeft' && currentIndex > 0) {
                    currentIndex--;
                    cards[currentIndex].click();
                    cards[currentIndex].scrollIntoView({ behavior: 'smooth', inline: 'center' });
                } else if (e.key === 'ArrowRight' && currentIndex < cards.length - 1) {
                    currentIndex++;
                    cards[currentIndex].click();
                    cards[currentIndex].scrollIntoView({ behavior: 'smooth', inline: 'center' });
                }
            });
        }

        // Start the app
        init();
        setupSmoothScroll();
        setupKeyboardNavigation();

        // Handle window resize
        window.addEventListener('resize', () => {
            // Recalculate layout if needed
            const cards = document.querySelectorAll('.weather-card');
            cards.forEach(card => {
                card.style.transition = 'none';
                setTimeout(() => {
                    card.style.transition = '';
                }, 100);
            });
        });
    </script>
</body>
</html>
