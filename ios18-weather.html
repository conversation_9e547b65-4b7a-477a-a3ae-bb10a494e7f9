<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 18 Weather</title>
    <style>
        /* iOS 18 Design System Variables */
        :root {
            /* Colors - Light Mode */
            --primary-bg: #F2F2F7;
            --card-bg: rgba(255, 255, 255, 0.8);
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --accent-blue: #007AFF;
            --accent-orange: #FF9500;
            --accent-red: #FF3B30;
            --accent-green: #34C759;
            
            /* Spacing */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            
            /* Border Radius */
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
            
            /* Shadows */
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.2);
            
            /* Blur */
            --blur-light: blur(10px);
            --blur-medium: blur(20px);
            --blur-heavy: blur(40px);
        }

        /* Dark Mode */
        @media (prefers-color-scheme: dark) {
            :root {
                --primary-bg: #000000;
                --card-bg: rgba(28, 28, 30, 0.8);
                --text-primary: #FFFFFF;
                --text-secondary: #8E8E93;
            }
        }

        /* SF Pro Font */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            background: var(--primary-bg);
            min-height: 100vh;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }

        /* Dynamic Background */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bg-sunny {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF6B6B 100%);
        }

        .bg-windy {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
        }

        .bg-rainy {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 50%, #74b9ff 100%);
        }

        .bg-snowy {
            background: linear-gradient(135deg, #ddd6fe 0%, #a78bfa 50%, #8b5cf6 100%);
        }

        /* Main Container */
        .weather-container {
            display: flex;
            height: 100vh;
            padding: var(--spacing-lg);
            gap: var(--spacing-lg);
            align-items: center;
            justify-content: center;
            overflow-x: auto;
            scroll-behavior: smooth;
        }

        /* Weather Card */
        .weather-card {
            min-width: 320px;
            height: 480px;
            background: var(--card-bg);
            backdrop-filter: var(--blur-medium);
            -webkit-backdrop-filter: var(--blur-medium);
            border-radius: var(--radius-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-heavy);
            padding: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .weather-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
        }

        .weather-card.active {
            transform: scale(1.05);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        /* Weather Icon */
        .weather-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 80px;
            position: relative;
            transition: all 0.3s ease;
        }

        .weather-icon.sunny {
            animation: rotate 20s linear infinite, pulse 2s ease-in-out infinite;
        }

        .weather-icon.windy {
            animation: shake 0.5s ease-in-out infinite alternate;
        }

        .weather-icon.rainy {
            animation: bounce 1s ease-in-out infinite;
        }

        .weather-icon.snowy {
            animation: float 3s ease-in-out infinite, rotate 30s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            100% { transform: translateX(5px); }
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Temperature */
        .temperature {
            font-size: 4rem;
            font-weight: 300;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--spacing-sm);
            letter-spacing: -2px;
        }

        /* Weather Description */
        .weather-description {
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        /* Location */
        .location {
            font-size: 1rem;
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            font-weight: 400;
        }

        /* Weather Details */
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-top: auto;
        }

        .detail-item {
            text-align: center;
            padding: var(--spacing-sm);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-sm);
            backdrop-filter: var(--blur-light);
        }

        .detail-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
        }

        .detail-value {
            font-size: 1.1rem;
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Theme Toggle */
        .theme-toggle {
            position: fixed;
            top: var(--spacing-lg);
            right: var(--spacing-lg);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--card-bg);
            backdrop-filter: var(--blur-medium);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .weather-container {
                flex-direction: column;
                height: auto;
                min-height: 100vh;
                overflow-x: hidden;
                overflow-y: auto;
                padding: var(--spacing-md);
            }
            
            .weather-card {
                min-width: 100%;
                height: 400px;
                margin-bottom: var(--spacing-md);
            }
            
            .temperature {
                font-size: 3rem;
            }
            
            .weather-icon {
                width: 100px;
                height: 100px;
                font-size: 60px;
            }
        }

        /* Loading Animation */
        .loading {
            opacity: 0;
            animation: fadeIn 0.8s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* Particle Effects */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: fall linear infinite;
        }

        .particle.snow {
            background: rgba(255, 255, 255, 0.9);
            animation: snowfall linear infinite, sway ease-in-out infinite;
        }

        .particle.rain {
            background: rgba(135, 206, 235, 0.8);
            border-radius: 1px;
            animation: rainfall linear infinite;
        }

        @keyframes fall {
            from {
                transform: translateY(-100px);
                opacity: 1;
            }
            to {
                transform: translateY(600px);
                opacity: 0;
            }
        }

        @keyframes snowfall {
            from {
                transform: translateY(-100px);
                opacity: 1;
            }
            to {
                transform: translateY(600px);
                opacity: 0;
            }
        }

        @keyframes sway {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(20px); }
        }

        @keyframes rainfall {
            from {
                transform: translateY(-100px) rotate(10deg);
                opacity: 1;
            }
            to {
                transform: translateY(600px) rotate(10deg);
                opacity: 0;
            }
        }

        @keyframes windMove {
            from {
                transform: translateX(-50px);
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
            to {
                transform: translateX(400px);
                opacity: 0;
            }
        }

        /* Card Glow Effects */
        .weather-card.sunny {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(255, 215, 0, 0.3);
        }

        .weather-card.windy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(116, 185, 255, 0.3);
        }

        .weather-card.rainy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(99, 110, 114, 0.3);
        }

        .weather-card.snowy {
            box-shadow: var(--shadow-heavy), 0 0 30px rgba(221, 214, 254, 0.3);
        }

        /* Smooth Scrolling */
        .weather-container {
            scroll-snap-type: x mandatory;
        }

        .weather-card {
            scroll-snap-align: center;
        }

        /* Enhanced Hover Effects */
        .weather-card:hover .weather-icon {
            transform: scale(1.1);
        }

        .weather-card:hover .temperature {
            color: var(--accent-blue);
        }

        /* Glass Morphism Enhancement */
        .weather-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-radius: var(--radius-xl);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="background-container bg-sunny" id="background"></div>
    
    <button class="theme-toggle" id="themeToggle">
        🌙
    </button>

    <div class="weather-container" id="weatherContainer">
        <!-- Weather cards will be generated by JavaScript -->
    </div>

    <script>
        // Weather data
        const weatherData = [
            {
                id: 'sunny',
                icon: '☀️',
                temperature: '28°',
                description: 'Sunny',
                location: 'Cupertino, CA',
                humidity: '45%',
                wind: '12 km/h',
                uv: 'High',
                visibility: '10 km',
                bgClass: 'bg-sunny'
            },
            {
                id: 'windy',
                icon: '💨',
                temperature: '22°',
                description: 'Windy',
                location: 'San Francisco, CA',
                humidity: '60%',
                wind: '35 km/h',
                pressure: '1013 hPa',
                visibility: '8 km',
                bgClass: 'bg-windy'
            },
            {
                id: 'rainy',
                icon: '🌧️',
                temperature: '18°',
                description: 'Heavy Rain',
                location: 'Seattle, WA',
                humidity: '85%',
                wind: '18 km/h',
                precipitation: '15 mm',
                visibility: '3 km',
                bgClass: 'bg-rainy'
            },
            {
                id: 'snowy',
                icon: '❄️',
                temperature: '-5°',
                description: 'Blizzard',
                location: 'Aspen, CO',
                humidity: '90%',
                wind: '45 km/h',
                snowfall: '25 cm',
                visibility: '1 km',
                bgClass: 'bg-snowy'
            }
        ];

        // DOM elements
        const weatherContainer = document.getElementById('weatherContainer');
        const background = document.getElementById('background');
        const themeToggle = document.getElementById('themeToggle');

        // Theme management
        let isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

        function toggleTheme() {
            isDarkMode = !isDarkMode;
            document.body.style.colorScheme = isDarkMode ? 'dark' : 'light';
            themeToggle.textContent = isDarkMode ? '☀️' : '🌙';
        }

        themeToggle.addEventListener('click', toggleTheme);

        // Create weather cards
        function createWeatherCard(data) {
            const card = document.createElement('div');
            card.className = `weather-card loading ${data.id}`;
            card.innerHTML = `
                <div class="weather-icon ${data.id}">${data.icon}</div>
                <div class="temperature">${data.temperature}</div>
                <div class="weather-description">${data.description}</div>
                <div class="location">${data.location}</div>
                <div class="weather-details">
                    <div class="detail-item">
                        <div class="detail-label">Humidity</div>
                        <div class="detail-value">${data.humidity}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Wind</div>
                        <div class="detail-value">${data.wind}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">${data.uv ? 'UV Index' : data.pressure ? 'Pressure' : data.precipitation ? 'Rain' : 'Snow'}</div>
                        <div class="detail-value">${data.uv || data.pressure || data.precipitation || data.snowfall}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Visibility</div>
                        <div class="detail-value">${data.visibility}</div>
                    </div>
                </div>
                <div class="particles" id="particles-${data.id}"></div>
            `;

            // Add click event to change background
            card.addEventListener('click', () => {
                // Smooth background transition
                background.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                background.className = `background-container ${data.bgClass}`;

                // Remove active class from all cards with animation
                document.querySelectorAll('.weather-card').forEach(c => {
                    c.classList.remove('active');
                    c.style.transform = '';
                });

                // Add active class to clicked card with enhanced animation
                card.classList.add('active');

                // Create particles based on weather type
                createParticles(data.id);

                // Haptic feedback simulation (visual feedback)
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 100);
            });

            // Add hover effects
            card.addEventListener('mouseenter', () => {
                if (!card.classList.contains('active')) {
                    card.style.transform = 'translateY(-4px) scale(1.01)';
                }
            });

            card.addEventListener('mouseleave', () => {
                if (!card.classList.contains('active')) {
                    card.style.transform = '';
                }
            });

            return card;
        }

        // Create particle effects
        function createParticles(weatherType) {
            // Clear all existing particles first
            document.querySelectorAll('.particles').forEach(container => {
                container.innerHTML = '';
            });

            const particlesContainer = document.getElementById(`particles-${weatherType}`);
            if (!particlesContainer) return;

            const particleCount = weatherType === 'snowy' ? 60 : weatherType === 'rainy' ? 80 : 0;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');

                if (weatherType === 'snowy') {
                    particle.className = 'particle snow';
                    particle.style.width = particle.style.height = (Math.random() * 6 + 3) + 'px';
                    particle.style.background = `rgba(255, 255, 255, ${Math.random() * 0.8 + 0.2})`;
                    particle.style.borderRadius = '50%';
                    particle.style.animationDuration = (Math.random() * 4 + 3) + 's';
                    particle.style.animationDelay = Math.random() * 2 + 's';

                    // Add sway animation
                    const swayDuration = (Math.random() * 3 + 2) + 's';
                    particle.style.setProperty('--sway-duration', swayDuration);

                } else if (weatherType === 'rainy') {
                    particle.className = 'particle rain';
                    particle.style.width = (Math.random() * 2 + 1) + 'px';
                    particle.style.height = (Math.random() * 15 + 10) + 'px';
                    particle.style.background = `rgba(135, 206, 235, ${Math.random() * 0.6 + 0.4})`;
                    particle.style.borderRadius = '1px';
                    particle.style.animationDuration = (Math.random() * 1 + 0.5) + 's';
                    particle.style.animationDelay = Math.random() * 1 + 's';
                }

                particle.style.left = Math.random() * 100 + '%';
                particlesContainer.appendChild(particle);
            }

            // Add special effects for sunny and windy weather
            if (weatherType === 'sunny') {
                addSunRays(particlesContainer);
            } else if (weatherType === 'windy') {
                addWindLines(particlesContainer);
            }
        }

        // Add sun rays effect
        function addSunRays(container) {
            for (let i = 0; i < 8; i++) {
                const ray = document.createElement('div');
                ray.style.position = 'absolute';
                ray.style.top = '20%';
                ray.style.left = '50%';
                ray.style.width = '2px';
                ray.style.height = '60px';
                ray.style.background = 'linear-gradient(to bottom, rgba(255, 215, 0, 0.6), transparent)';
                ray.style.transformOrigin = 'top center';
                ray.style.transform = `translateX(-50%) rotate(${i * 45}deg)`;
                ray.style.animation = `rotate 20s linear infinite`;
                ray.style.animationDelay = `${i * 0.1}s`;
                container.appendChild(ray);
            }
        }

        // Add wind lines effect
        function addWindLines(container) {
            for (let i = 0; i < 15; i++) {
                const line = document.createElement('div');
                line.style.position = 'absolute';
                line.style.top = Math.random() * 80 + 10 + '%';
                line.style.left = '-20px';
                line.style.width = (Math.random() * 40 + 20) + 'px';
                line.style.height = '1px';
                line.style.background = `rgba(116, 185, 255, ${Math.random() * 0.4 + 0.2})`;
                line.style.animation = `windMove ${Math.random() * 2 + 1}s linear infinite`;
                line.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(line);
            }
        }

        // Initialize the app
        function init() {
            // Add loading state to body
            document.body.style.opacity = '0';

            weatherData.forEach((data, index) => {
                const card = createWeatherCard(data);
                weatherContainer.appendChild(card);

                // Stagger the loading animation
                setTimeout(() => {
                    card.classList.remove('loading');
                    card.style.transform = 'translateY(20px)';
                    card.style.opacity = '0';

                    // Animate card entrance
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                        card.style.transform = 'translateY(0)';
                        card.style.opacity = '1';
                    }, 50);
                }, index * 150);
            });

            // Set initial theme
            themeToggle.textContent = isDarkMode ? '☀️' : '🌙';

            // Fade in the entire app
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.8s ease';
                document.body.style.opacity = '1';
            }, 200);

            // Set first card as active
            setTimeout(() => {
                const firstCard = weatherContainer.querySelector('.weather-card');
                if (firstCard) {
                    firstCard.click();
                }
            }, 1200);
        }

        // Smooth scroll for horizontal navigation
        function setupSmoothScroll() {
            let isScrolling = false;

            weatherContainer.addEventListener('wheel', (e) => {
                if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
                    e.preventDefault();

                    if (!isScrolling) {
                        isScrolling = true;
                        weatherContainer.scrollBy({
                            left: e.deltaX > 0 ? 340 : -340,
                            behavior: 'smooth'
                        });

                        setTimeout(() => {
                            isScrolling = false;
                        }, 500);
                    }
                }
            });
        }

        // Add keyboard navigation
        function setupKeyboardNavigation() {
            let currentIndex = 0;

            document.addEventListener('keydown', (e) => {
                const cards = document.querySelectorAll('.weather-card');

                if (e.key === 'ArrowLeft' && currentIndex > 0) {
                    currentIndex--;
                    cards[currentIndex].click();
                    cards[currentIndex].scrollIntoView({ behavior: 'smooth', inline: 'center' });
                } else if (e.key === 'ArrowRight' && currentIndex < cards.length - 1) {
                    currentIndex++;
                    cards[currentIndex].click();
                    cards[currentIndex].scrollIntoView({ behavior: 'smooth', inline: 'center' });
                }
            });
        }

        // Start the app
        init();
        setupSmoothScroll();
        setupKeyboardNavigation();

        // Handle window resize
        window.addEventListener('resize', () => {
            // Recalculate layout if needed
            const cards = document.querySelectorAll('.weather-card');
            cards.forEach(card => {
                card.style.transition = 'none';
                setTimeout(() => {
                    card.style.transition = '';
                }, 100);
            });
        });
    </script>
</body>
</html>
