# iOS 18 Weather Page

一个严格遵循 iOS 18 设计语言的横版天气页面，展示了现代 Web 技术与 Apple 设计美学的完美结合。

## 🌟 特性

### 设计特性
- **iOS 18 设计语言**: 严格遵循 Apple 的设计规范
- **SF Pro 字体**: 使用 Apple 官方字体系列
- **毛玻璃效果**: 真实的 Glass Morphism 视觉效果
- **深色/浅色模式**: 自动适配系统主题，支持手动切换
- **响应式设计**: 完美适配桌面和移动设备

### 交互特性
- **4 种天气状态**: 晴天、大风、暴雨、暴雪
- **动态背景**: 根据选择的天气卡片切换背景渐变
- **粒子效果**: 雨滴、雪花、阳光射线、风线等动态效果
- **微交互动画**: 悬停、点击、切换等精致的动画效果
- **键盘导航**: 支持左右箭头键切换天气卡片
- **平滑滚动**: 横向滚动和卡片切换动画

### 技术特性
- **纯前端实现**: HTML5 + CSS3 + Vanilla JavaScript
- **现代 CSS**: CSS Grid、Flexbox、CSS 变量、动画
- **性能优化**: 硬件加速、流畅的 60fps 动画
- **无依赖**: 不需要任何外部库或框架

## 🎨 视觉效果

### 天气卡片
每个天气卡片包含：
- 动态天气图标（带有特定动画效果）
- 温度显示
- 天气描述
- 位置信息
- 详细天气数据（湿度、风速、能见度等）

### 动画效果
- **晴天**: 旋转和脉冲动画，阳光射线效果
- **大风**: 摇摆动画，风线粒子效果
- **暴雨**: 弹跳动画，雨滴下落效果
- **暴雪**: 漂浮和旋转动画，雪花飘落效果

## 🚀 使用方法

### 快速启动
1. 双击 `start-weather-demo.bat` 文件
2. 或直接在浏览器中打开 `ios18-weather.html`

### 交互操作
- **点击卡片**: 切换天气状态和背景
- **悬停卡片**: 查看悬停效果
- **键盘导航**: 使用左右箭头键切换
- **主题切换**: 点击右上角的主题按钮
- **横向滚动**: 在移动设备上滑动或使用鼠标滚轮

## 🛠️ 技术实现

### CSS 特性
- CSS 变量用于主题管理
- CSS Grid 和 Flexbox 布局
- backdrop-filter 实现毛玻璃效果
- CSS 动画和过渡效果
- 媒体查询实现响应式设计

### JavaScript 功能
- 动态卡片生成
- 粒子系统
- 主题切换
- 键盘和鼠标事件处理
- 平滑滚动控制

### 设计原则
- 遵循 iOS 18 的间距和圆角规范
- 使用 Apple 的色彩系统
- 实现真实的物理动画效果
- 保持一致的视觉层次

## 📱 兼容性

- **现代浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **移动设备**: iOS Safari, Chrome Mobile, Samsung Internet
- **响应式**: 支持从手机到大屏显示器的所有尺寸

## 🎯 设计目标

这个项目旨在展示：
1. 如何使用 Web 技术实现 Apple 级别的设计质量
2. 现代 CSS 和 JavaScript 的强大功能
3. 用户体验设计的重要性
4. 性能优化和流畅动画的实现

## 📄 文件结构

```
├── ios18-weather.html          # 主页面文件
├── start-weather-demo.bat      # Windows 启动脚本
└── iOS18-Weather-README.md     # 项目说明文档
```

## 🔧 自定义

您可以轻松自定义：
- 天气数据和位置
- 颜色主题和渐变
- 动画效果和时长
- 粒子效果参数
- 响应式断点

---

**注意**: 这是一个演示项目，展示了 iOS 18 设计语言在 Web 平台上的实现。所有设计元素都严格遵循 Apple 的设计规范。
