/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 - 默认霓虹主题 */
:root {
    --primary-color: #00ff88;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --background-dark: #1a1a2e;
    --background-light: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --border-radius: 12px;
    --shadow-primary: 0 8px 32px rgba(0, 255, 136, 0.3);
    --shadow-secondary: 0 4px 16px rgba(0, 0, 0, 0.4);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --snake-color: #00ff88;
    --snake-head-color: #00cc6a;
    --food-color: #ff6b6b;
    --special-food-golden: #ffd700;
    --special-food-diamond: #4ecdc4;
    --obstacle-color: #8b4513;
    --canvas-background: #0f0f23;
}

/* 经典主题 */
[data-theme="classic"] {
    --primary-color: #228B22;
    --secondary-color: #DC143C;
    --accent-color: #4169E1;
    --background-dark: #2F4F4F;
    --background-light: #708090;
    --text-primary: #F5F5DC;
    --text-secondary: #D3D3D3;
    --shadow-primary: 0 8px 32px rgba(34, 139, 34, 0.3);
    --gradient-primary: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
    --gradient-secondary: linear-gradient(135deg, #DC143C 0%, #FF6347 100%);
    --gradient-success: linear-gradient(135deg, #4169E1 0%, #87CEEB 100%);
    --snake-color: #228B22;
    --snake-head-color: #32CD32;
    --food-color: #DC143C;
    --special-food-golden: #FFD700;
    --special-food-diamond: #4169E1;
    --obstacle-color: #8B4513;
    --canvas-background: #F5F5DC;
}

/* 森林主题 */
[data-theme="forest"] {
    --primary-color: #228B22;
    --secondary-color: #8B4513;
    --accent-color: #32CD32;
    --background-dark: #0B4D0B;
    --background-light: #1B5E1B;
    --text-primary: #F0FFF0;
    --text-secondary: #98FB98;
    --shadow-primary: 0 8px 32px rgba(34, 139, 34, 0.4);
    --gradient-primary: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
    --gradient-secondary: linear-gradient(135deg, #8B4513 0%, #CD853F 100%);
    --gradient-success: linear-gradient(135deg, #32CD32 0%, #7CFC00 100%);
    --snake-color: #228B22;
    --snake-head-color: #32CD32;
    --food-color: #FF4500;
    --special-food-golden: #FFD700;
    --special-food-diamond: #00CED1;
    --obstacle-color: #8B4513;
    --canvas-background: #0B2F0B;
}

/* 海洋主题 */
[data-theme="ocean"] {
    --primary-color: #00CED1;
    --secondary-color: #FF6347;
    --accent-color: #4682B4;
    --background-dark: #001F3F;
    --background-light: #003366;
    --text-primary: #F0F8FF;
    --text-secondary: #B0E0E6;
    --shadow-primary: 0 8px 32px rgba(0, 206, 209, 0.4);
    --gradient-primary: linear-gradient(135deg, #00CED1 0%, #20B2AA 100%);
    --gradient-secondary: linear-gradient(135deg, #FF6347 0%, #FF7F50 100%);
    --gradient-success: linear-gradient(135deg, #4682B4 0%, #87CEEB 100%);
    --snake-color: #00CED1;
    --snake-head-color: #20B2AA;
    --food-color: #FF6347;
    --special-food-golden: #FFD700;
    --special-food-diamond: #4682B4;
    --obstacle-color: #2F4F4F;
    --canvas-background: #001122;
}

/* 日落主题 */
[data-theme="sunset"] {
    --primary-color: #FF6B35;
    --secondary-color: #F7931E;
    --accent-color: #FFD23F;
    --background-dark: #2C1810;
    --background-light: #3D2817;
    --text-primary: #FFF8DC;
    --text-secondary: #FFEAA7;
    --shadow-primary: 0 8px 32px rgba(255, 107, 53, 0.4);
    --gradient-primary: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    --gradient-secondary: linear-gradient(135deg, #F7931E 0%, #FFD23F 100%);
    --gradient-success: linear-gradient(135deg, #FFD23F 0%, #FFEAA7 100%);
    --snake-color: #FF6B35;
    --snake-head-color: #F7931E;
    --food-color: #FFD23F;
    --special-food-golden: #FFD700;
    --special-food-diamond: #FF1493;
    --obstacle-color: #8B4513;
    --canvas-background: #1A0F0A;
}

/* 赛博朋克主题 */
[data-theme="cyberpunk"] {
    --primary-color: #FF00FF;
    --secondary-color: #00FFFF;
    --accent-color: #FFFF00;
    --background-dark: #0A0A0A;
    --background-light: #1A1A1A;
    --text-primary: #FFFFFF;
    --text-secondary: #FF00FF;
    --shadow-primary: 0 8px 32px rgba(255, 0, 255, 0.5);
    --gradient-primary: linear-gradient(135deg, #FF00FF 0%, #8A2BE2 100%);
    --gradient-secondary: linear-gradient(135deg, #00FFFF 0%, #0080FF 100%);
    --gradient-success: linear-gradient(135deg, #FFFF00 0%, #FF8C00 100%);
    --snake-color: #FF00FF;
    --snake-head-color: #8A2BE2;
    --food-color: #00FFFF;
    --special-food-golden: #FFFF00;
    --special-food-diamond: #FF1493;
    --obstacle-color: #696969;
    --canvas-background: #000000;
}

/* 主体样式 */
body {
    font-family: 'Orbitron', monospace;
    background: var(--background-dark);
    color: var(--text-primary);
    min-height: 100vh;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    overflow-x: hidden;
}

/* 游戏容器 */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

/* 游戏标题 */
.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-title {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.title-icon {
    font-size: 2.5rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

/* 游戏信息面板 */
.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-secondary);
}

.info-panel {
    display: flex;
    gap: 30px;
}

.score-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.score-display .label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.score-display .value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* 游戏设置 */
.game-settings {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.setting-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 8px 12px;
    font-family: inherit;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.3);
}

.setting-select:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.setting-select option {
    background: var(--background-dark);
    color: var(--text-primary);
    padding: 8px;
    border: none;
}

/* 主题选择器特殊样式 */
#theme option[value="neon"] {
    background-color: #FFB6C1;
    color: #191970;
}

#theme option[value="classic"] {
    background-color: #228B22;
    color: white;
}

#theme option[value="forest"] {
    background-color: #228B22;
    color: white;
}

#theme option[value="ocean"] {
    background-color: #00CED1;
    color: white;
}

#theme option[value="sunset"] {
    background-color: #FF6B35;
    color: white;
}

#theme option[value="cyberpunk"] {
    background-color: #FF00FF;
    color: white;
}

/* 画布容器 */
.canvas-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-primary);
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

#game-canvas {
    display: block;
    background: linear-gradient(45deg, #0f0f23 25%, transparent 25%),
                linear-gradient(-45deg, #0f0f23 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #0f0f23 75%),
                linear-gradient(-45deg, transparent 75%, #0f0f23 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* 游戏覆盖层 */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: opacity 0.3s ease;
}

.overlay-content {
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    max-width: 400px;
}

#overlay-title {
    font-size: 2rem;
    margin-bottom: 15px;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#overlay-message {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
}

.overlay-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    color: var(--primary-color);
    font-weight: 700;
}

/* 控制面板 */
.control-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 600px;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn.primary {
    background: var(--gradient-success);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.control-btn.secondary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.control-btn.danger {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
}

.control-btn.info {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.control-btn.help {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.control-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-icon {
    font-size: 1.2rem;
}

/* 设置面板 */
.settings-panel {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-group {
    display: flex;
    align-items: center;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.setting-label:hover {
    color: var(--text-primary);
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 移动端控制器 */
.mobile-controls {
    display: none;
    margin-top: 20px;
}

.direction-pad {
    display: grid;
    grid-template-areas: 
        ". up ."
        "left . right"
        ". down .";
    gap: 10px;
    justify-items: center;
}

.direction-btn {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.direction-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.direction-btn:active {
    transform: scale(0.95);
}

.direction-btn.up { grid-area: up; }
.direction-btn.down { grid-area: down; }
.direction-btn.left { grid-area: left; }
.direction-btn.right { grid-area: right; }

.horizontal-controls {
    display: flex;
    gap: 80px;
    grid-area: left / left / right / right;
    justify-content: space-between;
    width: 100%;
}

/* 游戏说明 */
.game-instructions {
    width: 100%;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-instructions h3 {
    text-align: center;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.instructions-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.instruction-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.instruction-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .title-icon {
        font-size: 1.8rem;
    }
    
    .game-info {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }
    
    .info-panel {
        gap: 20px;
    }
    
    #game-canvas {
        width: 100%;
        height: auto;
        max-width: 400px;
        max-height: 400px;
    }
    
    .mobile-controls {
        display: block;
    }
    
    .game-controls {
        gap: 10px;
    }
    
    .control-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    .settings-panel {
        gap: 15px;
        padding: 15px;
    }
    
    .instructions-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .info-panel {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .score-display .value {
        font-size: 1.5rem;
    }
    
    .control-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
    
    .direction-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .horizontal-controls {
        gap: 60px;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.5); }
    50% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.8); }
    100% { box-shadow: 0 0 5px rgba(0, 255, 136, 0.5); }
}

.score-display .value {
    animation: glow 2s ease-in-out infinite;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

.fade-out {
    opacity: 0;
    pointer-events: none;
}

.fade-in {
    opacity: 1;
    pointer-events: all;
}

/* 成就弹窗样式 */
.achievement-popup {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 0;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.5s ease;
    max-width: 350px;
    box-shadow: var(--shadow-primary);
}

.achievement-popup.show {
    transform: translateX(0);
}

.achievement-content {
    padding: 20px;
}

.achievement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
}

.achievement-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.achievement-body {
    display: flex;
    align-items: center;
    gap: 15px;
}

.achievement-icon {
    font-size: 3rem;
    animation: bounce 0.6s ease;
}

.achievement-info h4 {
    margin: 0 0 5px 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.achievement-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 成就列表模态框 */
.achievements-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}

.achievements-modal.show {
    display: flex;
}

.modal-content {
    background: var(--background-light);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-primary);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: var(--background-dark);
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

.achievements-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.achievements-stats .stat-item {
    text-align: center;
}

.achievements-stats .stat-label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.achievements-stats .stat-value {
    display: block;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
}

.achievements-list {
    display: grid;
    gap: 15px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.achievement-item.unlocked {
    border-left-color: var(--primary-color);
    background: rgba(var(--primary-color), 0.1);
}

.achievement-item.locked {
    opacity: 0.6;
    border-left-color: var(--text-secondary);
}

.achievement-item-icon {
    font-size: 2rem;
    width: 50px;
    text-align: center;
}

.achievement-item-info {
    flex: 1;
}

.achievement-item-title {
    margin: 0 0 5px 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.achievement-item-description {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.achievement-item-progress {
    text-align: right;
    min-width: 80px;
}

.achievement-progress-text {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
}

.achievement-progress-bar {
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    margin-top: 5px;
    overflow: hidden;
}

.achievement-progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

/* 动画效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes messageSlide {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 游戏消息样式 */
.game-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    color: var(--primary-color);
    padding: 15px 25px;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: bold;
    z-index: 1000;
    animation: messageSlide 0.5s ease;
    pointer-events: none;
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 20px rgba(var(--primary-color), 0.3);
}

/* 玩法说明弹窗 */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}

.help-modal.show {
    display: flex;
}

.help-section {
    margin-bottom: 30px;
}

.help-section h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.help-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.help-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.help-icon {
    font-size: 1.8rem;
    min-width: 40px;
    text-align: center;
}

.help-content h5 {
    margin: 0 0 5px 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.help-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.help-section:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .help-grid {
        grid-template-columns: 1fr;
    }

    .help-item {
        padding: 12px;
    }

    .help-icon {
        font-size: 1.5rem;
        min-width: 35px;
    }
}
