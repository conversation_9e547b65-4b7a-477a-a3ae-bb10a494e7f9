# 🔧 贪吃蛇游戏故障排除指南

## 🚨 常见问题及解决方案

### 1. 游戏无法启动或显示空白页面

**可能原因：**
- 浏览器兼容性问题
- JavaScript被禁用
- 文件路径问题

**解决方案：**
```bash
# 方法1: 使用本地服务器（推荐）
# Windows用户：
双击 start-server.bat

# Mac/Linux用户：
chmod +x start-server.sh
./start-server.sh

# 然后访问 http://localhost:8000
```

**方法2: 检查浏览器控制台**
1. 按F12打开开发者工具
2. 查看Console标签页是否有错误信息
3. 查看Network标签页检查文件是否正确加载

### 2. 游戏界面显示但无法操作

**检查步骤：**
1. 确认JavaScript已启用
2. 检查是否有JavaScript错误
3. 尝试点击"开始游戏"按钮
4. 测试键盘控制（方向键或WASD）

**调试版本：**
打开 `debug.html` 文件查看详细的调试信息

### 3. 样式显示异常

**可能原因：**
- CSS文件未正确加载
- 字体加载失败
- 浏览器缓存问题

**解决方案：**
1. 强制刷新页面（Ctrl+F5）
2. 清除浏览器缓存
3. 检查网络连接（Google Fonts需要网络）

### 4. 音效无法播放

**原因：**
- 音频文件缺失
- 浏览器音频策略限制
- 音效被关闭

**解决方案：**
1. 检查 `sounds/` 文件夹中是否有音频文件
2. 确保音效开关已开启
3. 在用户交互后音效才能播放（浏览器限制）

### 5. 移动端控制不响应

**检查项目：**
1. 确认在移动设备上访问
2. 检查触摸事件是否被阻止
3. 尝试刷新页面

### 6. 游戏性能问题

**优化建议：**
1. 关闭其他浏览器标签页
2. 降低游戏难度（减慢速度）
3. 关闭特殊效果（障碍物、特殊食物）

## 🔍 详细诊断步骤

### 步骤1: 基础检查
```javascript
// 在浏览器控制台中运行以下代码
console.log('Canvas元素:', document.getElementById('game-canvas'));
console.log('游戏对象:', typeof game !== 'undefined' ? game : '未定义');
```

### 步骤2: 检查文件加载
1. 打开开发者工具 → Network标签
2. 刷新页面
3. 确认所有文件（HTML、CSS、JS）都成功加载（状态码200）

### 步骤3: JavaScript错误检查
1. 打开开发者工具 → Console标签
2. 查看是否有红色错误信息
3. 如有错误，记录错误信息

### 步骤4: 功能测试
1. 测试按钮点击响应
2. 测试键盘输入
3. 测试游戏逻辑（移动、吃食物、碰撞）

## 🌐 浏览器兼容性

### 支持的浏览器：
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 不支持的浏览器：
- ❌ Internet Explorer
- ❌ 过旧版本的移动浏览器

## 📱 移动端特殊说明

### iOS Safari:
- 可能需要用户交互才能播放音效
- 建议使用最新版本

### Android Chrome:
- 性能较好，功能完整
- 支持所有特性

## 🛠️ 开发者调试

### 启用详细日志：
```javascript
// 在浏览器控制台中运行
localStorage.setItem('debug', 'true');
location.reload();
```

### 重置游戏数据：
```javascript
// 清除本地存储的游戏数据
localStorage.removeItem('snakeHighScore');
location.reload();
```

### 手动测试游戏对象：
```javascript
// 检查游戏状态
if (typeof game !== 'undefined') {
    console.log('游戏状态:', game.gameState);
    console.log('蛇的位置:', game.snake.body);
    console.log('食物位置:', game.food.position);
}
```

## 📞 获取帮助

如果以上方法都无法解决问题：

1. **检查浏览器版本**：确保使用支持的浏览器版本
2. **尝试其他设备**：在不同设备上测试
3. **查看debug.html**：使用简化版本进行测试
4. **记录错误信息**：截图或复制控制台错误信息

## 🔄 重新安装

如果问题持续存在：

1. 重新下载所有游戏文件
2. 确保文件结构完整：
   ```
   snake-game/
   ├── index.html
   ├── styles.css
   ├── script.js
   ├── debug.html
   ├── sounds/
   └── README.md
   ```
3. 使用本地服务器运行

---

💡 **提示**：大多数问题都可以通过使用本地HTTP服务器来解决，而不是直接打开HTML文件。
