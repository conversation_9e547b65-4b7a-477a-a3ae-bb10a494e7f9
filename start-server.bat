@echo off
echo 正在启动本地服务器...
echo.
echo 请确保已安装Python 3.x
echo.
echo 服务器启动后，请在浏览器中访问：
echo http://localhost:8000
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 尝试使用Python 3
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    REM 如果Python 3失败，尝试Python 2
    echo Python 3未找到，尝试Python 2...
    python -m SimpleHTTPServer 8000 2>nul
    if %errorlevel% neq 0 (
        echo.
        echo 错误：未找到Python！
        echo 请安装Python 3.x 或 Python 2.x
        echo.
        echo 或者您可以：
        echo 1. 直接双击 index.html 文件
        echo 2. 使用其他本地服务器（如Live Server扩展）
        echo.
        pause
    )
)
