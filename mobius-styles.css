/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 300;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 主要内容区域 */
main {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    margin-bottom: 30px;
}

/* 3D场景容器 */
#scene-container {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

#mobius-canvas {
    width: 100%;
    height: 600px;
    display: block;
    border-radius: 15px;
}

/* 控制面板 */
.controls-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    min-width: 200px;
}

.controls-panel h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.control-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.control-group input[type="range"] {
    width: 100%;
    margin-top: 5px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.control-group button {
    width: 100%;
    padding: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.control-group button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 信息面板 */
.info-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    height: fit-content;
}

.info-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.info-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-section h3 {
    color: #333;
    margin-bottom: 12px;
    font-size: 1.2rem;
    font-weight: 600;
}

.info-section p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 10px;
}

.info-section ul {
    color: #666;
    padding-left: 20px;
}

.info-section li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.info-section li strong {
    color: #333;
    font-weight: 600;
}

/* 页脚 */
footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    padding: 20px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    main {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .controls-panel {
        position: static;
        margin-top: 20px;
        width: 100%;
    }
    
    #scene-container {
        height: 500px;
    }
    
    #mobius-canvas {
        height: 500px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .controls-panel {
        padding: 15px;
    }
    
    .info-panel {
        padding: 20px;
    }
    
    #scene-container {
        height: 400px;
    }
    
    #mobius-canvas {
        height: 400px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }
    
    .info-section h3 {
        font-size: 1.1rem;
    }
    
    #scene-container {
        height: 350px;
    }
    
    #mobius-canvas {
        height: 350px;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 600px;
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    padding: 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

/* 错误状态的样式 */
.loading[style*="color: #e74c3c"] .loading-spinner {
    border-top-color: #e74c3c;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.6; transform: scale(0.95); }
    100% { opacity: 1; transform: scale(1.05); }
}

/* 重试按钮样式 */
.loading button {
    padding: 12px 24px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.loading button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 提示工具样式 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tooltip.show {
    opacity: 1;
}

/* 性能优化：GPU加速 */
#mobius-canvas,
.controls-panel,
.info-panel {
    transform: translateZ(0);
    will-change: transform;
}
