<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>莫比乌斯环 3D 可视化 (离线版)</title>
    <link rel="stylesheet" href="mobius-styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <header>
            <h1>莫比乌斯环 3D 可视化 (离线版)</h1>
            <p class="subtitle">无需网络连接的本地版本</p>
        </header>

        <!-- 主要内容区域 -->
        <main>
            <!-- 3D 场景容器 -->
            <div id="scene-container">
                <canvas id="mobius-canvas" width="800" height="600"></canvas>
                
                <!-- 控制面板 -->
                <div class="controls-panel">
                    <h3>交互控制</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="auto-rotate" checked>
                            自动旋转
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="show-particle" checked>
                            显示轨迹演示
                        </label>
                    </div>
                    <div class="control-group">
                        <label>旋转速度:</label>
                        <input type="range" id="rotation-speed" min="0.5" max="3" step="0.1" value="1">
                    </div>
                    <div class="control-group">
                        <button id="reset-view">重置视角</button>
                    </div>
                </div>
            </div>

            <!-- 教育信息面板 -->
            <div class="info-panel">
                <div class="info-section">
                    <h3>🎯 什么是莫比乌斯环？</h3>
                    <p>莫比乌斯环是一个具有奇特性质的几何体，它只有一个面和一个边界。如果你沿着它的表面走，你会发现你能够到达"另一面"而不需要跨越边界。</p>
                </div>
                
                <div class="info-section">
                    <h3>🔬 数学特性</h3>
                    <ul>
                        <li><strong>单面性</strong>：莫比乌斯环只有一个连续的表面</li>
                        <li><strong>单边界</strong>：只有一条连续的边界线</li>
                        <li><strong>不可定向</strong>：没有明确的"内侧"和"外侧"</li>
                        <li><strong>拓扑性质</strong>：欧拉示性数为0</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>🎮 交互说明</h3>
                    <ul>
                        <li><strong>旋转</strong>：按住鼠标左键拖拽</li>
                        <li><strong>缩放</strong>：使用鼠标滚轮</li>
                        <li><strong>轨迹演示</strong>：观察红色小球沿表面的连续运动</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3>💡 离线版本说明</h3>
                    <p>此版本使用Canvas 2D API实现，无需网络连接即可运行。虽然视觉效果较简化，但能完整展示莫比乌斯环的数学特性。</p>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer>
            <p>Canvas 2D 离线版本 | 数学可视化项目</p>
        </footer>
    </div>

    <script>
        /**
         * 莫比乌斯环 Canvas 2D 实现 (离线版)
         */
        class MobiusRingOffline {
            constructor() {
                this.canvas = document.getElementById('mobius-canvas');
                this.ctx = this.canvas.getContext('2d');
                
                // 基础参数
                this.centerX = 400;
                this.centerY = 300;
                this.scale = 100;
                this.rotationX = 0;
                this.rotationY = 0;
                this.rotationZ = 0;
                this.autoRotate = true;
                this.showParticle = true;
                this.rotationSpeed = 1;
                this.particleT = 0;
                
                // 鼠标交互
                this.isDragging = false;
                this.lastMouseX = 0;
                this.lastMouseY = 0;
                
                this.init();
                this.setupEvents();
                this.animate();
            }
            
            init() {
                // 调整画布大小
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());
            }
            
            resizeCanvas() {
                const container = this.canvas.parentElement;
                this.canvas.width = container.clientWidth;
                this.canvas.height = Math.min(600, container.clientHeight);
                this.centerX = this.canvas.width / 2;
                this.centerY = this.canvas.height / 2;
            }
            
            // 莫比乌斯环参数方程
            mobiusPoint(u, v) {
                const x = Math.cos(u) * (3 + v * Math.cos(u / 2));
                const y = Math.sin(u) * (3 + v * Math.cos(u / 2));
                const z = v * Math.sin(u / 2);
                return { x, y, z };
            }
            
            // 3D到2D投影
            project3D(point) {
                // 应用旋转变换
                const cosX = Math.cos(this.rotationX);
                const sinX = Math.sin(this.rotationX);
                const cosY = Math.cos(this.rotationY);
                const sinY = Math.sin(this.rotationY);
                const cosZ = Math.cos(this.rotationZ);
                const sinZ = Math.sin(this.rotationZ);
                
                // 旋转矩阵应用
                let x = point.x;
                let y = point.y * cosX - point.z * sinX;
                let z = point.y * sinX + point.z * cosX;
                
                let nx = x * cosY + z * sinY;
                let ny = y;
                let nz = -x * sinY + z * cosY;
                
                x = nx * cosZ - ny * sinZ;
                y = nx * sinZ + ny * cosZ;
                z = nz;
                
                // 透视投影
                const distance = 10;
                const scale = this.scale * distance / (distance + z);
                
                return {
                    x: this.centerX + x * scale,
                    y: this.centerY + y * scale,
                    depth: z
                };
            }
            
            render() {
                // 清空画布
                this.ctx.fillStyle = '#f0f8ff';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 生成莫比乌斯环的点
                const points = [];
                const uSteps = 60;
                const vSteps = 8;
                
                for (let i = 0; i <= uSteps; i++) {
                    for (let j = 0; j <= vSteps; j++) {
                        const u = (i / uSteps) * Math.PI * 2;
                        const v = ((j / vSteps) - 0.5) * 0.6;
                        
                        const point3D = this.mobiusPoint(u, v);
                        const point2D = this.project3D(point3D);
                        
                        points.push({
                            ...point2D,
                            u: i,
                            v: j,
                            color: this.getPointColor(u, v)
                        });
                    }
                }
                
                // 按深度排序（画家算法）
                points.sort((a, b) => b.depth - a.depth);
                
                // 绘制莫比乌斯环
                this.drawMobiusRing(points, uSteps, vSteps);
                
                // 绘制轨迹小球
                if (this.showParticle) {
                    this.drawParticle();
                }
                
                // 绘制信息
                this.drawInfo();
            }
            
            drawMobiusRing(points, uSteps, vSteps) {
                // 绘制网格线
                this.ctx.strokeStyle = '#4a90e2';
                this.ctx.lineWidth = 1;
                
                // U方向的线
                for (let i = 0; i <= uSteps; i += 3) {
                    this.ctx.beginPath();
                    let first = true;
                    for (let j = 0; j <= vSteps; j++) {
                        const point = points.find(p => p.u === i && p.v === j);
                        if (point) {
                            if (first) {
                                this.ctx.moveTo(point.x, point.y);
                                first = false;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    this.ctx.stroke();
                }
                
                // V方向的线
                for (let j = 0; j <= vSteps; j += 2) {
                    this.ctx.beginPath();
                    let first = true;
                    for (let i = 0; i <= uSteps; i++) {
                        const point = points.find(p => p.u === i && p.v === j);
                        if (point) {
                            if (first) {
                                this.ctx.moveTo(point.x, point.y);
                                first = false;
                            } else {
                                this.ctx.lineTo(point.x, point.y);
                            }
                        }
                    }
                    this.ctx.stroke();
                }
                
                // 绘制表面点
                points.forEach(point => {
                    this.ctx.fillStyle = point.color;
                    this.ctx.beginPath();
                    this.ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
                    this.ctx.fill();
                });
            }
            
            getPointColor(u, v) {
                const hue = ((u / (Math.PI * 2)) * 0.6 + 0.1) * 360;
                const saturation = 70;
                const lightness = 50 + v * 20;
                return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            }
            
            drawParticle() {
                const u = this.particleT * Math.PI * 2;
                const v = 0;
                
                const point3D = this.mobiusPoint(u, v);
                const point2D = this.project3D(point3D);
                
                // 绘制小球
                this.ctx.fillStyle = '#ff4444';
                this.ctx.beginPath();
                this.ctx.arc(point2D.x, point2D.y, 6, 0, Math.PI * 2);
                this.ctx.fill();
                
                // 绘制小球边框
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
                
                // 更新小球位置
                this.particleT += 0.01;
                if (this.particleT > 2) {
                    this.particleT = 0;
                }
            }
            
            drawInfo() {
                this.ctx.fillStyle = '#333';
                this.ctx.font = '14px Arial';
                this.ctx.fillText('拖拽旋转 | 滚轮缩放', 10, 25);
            }
            
            setupEvents() {
                // 鼠标事件
                this.canvas.addEventListener('mousedown', (e) => {
                    this.isDragging = true;
                    this.lastMouseX = e.clientX;
                    this.lastMouseY = e.clientY;
                });
                
                this.canvas.addEventListener('mousemove', (e) => {
                    if (this.isDragging) {
                        const deltaX = e.clientX - this.lastMouseX;
                        const deltaY = e.clientY - this.lastMouseY;
                        
                        this.rotationY += deltaX * 0.01;
                        this.rotationX += deltaY * 0.01;
                        
                        this.lastMouseX = e.clientX;
                        this.lastMouseY = e.clientY;
                    }
                });
                
                this.canvas.addEventListener('mouseup', () => {
                    this.isDragging = false;
                });
                
                this.canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    this.scale *= (1 - e.deltaY * 0.001);
                    this.scale = Math.max(20, Math.min(200, this.scale));
                });
                
                // 控制面板事件
                document.getElementById('auto-rotate').addEventListener('change', (e) => {
                    this.autoRotate = e.target.checked;
                });
                
                document.getElementById('show-particle').addEventListener('change', (e) => {
                    this.showParticle = e.target.checked;
                });
                
                document.getElementById('rotation-speed').addEventListener('input', (e) => {
                    this.rotationSpeed = parseFloat(e.target.value);
                });
                
                document.getElementById('reset-view').addEventListener('click', () => {
                    this.rotationX = 0;
                    this.rotationY = 0;
                    this.rotationZ = 0;
                    this.scale = 100;
                });
            }
            
            animate() {
                if (this.autoRotate) {
                    this.rotationY += 0.005 * this.rotationSpeed;
                }
                
                this.render();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.mobiusOffline = new MobiusRingOffline();
            } catch (error) {
                console.error('初始化失败:', error);
                document.getElementById('scene-container').innerHTML = 
                    '<div class="loading">初始化失败，请检查浏览器支持</div>';
            }
        });
    </script>
</body>
</html>


