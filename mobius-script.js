/**
 * 莫比乌斯环 3D 可视化
 * 使用 Three.js 实现交互式莫比乌斯环模型
 */

class MobiusRingVisualizer {
    constructor() {
        // 基础变量
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.mobiusRing = null;
        this.particle = null;
        this.animationId = null;
        
        // 动画参数
        this.autoRotate = true;
        this.showParticle = true;
        this.rotationSpeed = 0.005;
        this.particleT = 0; // 小球在莫比乌斯环上的参数
        
        // 初始化
        this.init();
        this.setupEventListeners();
        this.animate();
    }

    /**
     * 初始化3D场景
     */
    init() {
        const canvas = document.getElementById('mobius-canvas');
        const container = document.getElementById('scene-container');
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f8ff);

        // 创建相机
        const width = container.clientWidth;
        const height = container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(0, 0, 8);

        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas, 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // 创建轨道控制器
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.minDistance = 3;
        this.controls.maxDistance = 20;

        // 创建莫比乌斯环
        this.createMobiusRing();
        
        // 创建光照
        this.createLighting();
        
        // 创建轨迹演示小球
        this.createParticle();
        
        // 添加网格辅助线（可选）
        // this.addHelpers();
    }

    /**
     * 创建莫比乌斯环几何体
     */
    createMobiusRing() {
        // 莫比乌斯环参数方程实现
        function mobiusFunction(u, v, target) {
            // u: [0, 2π] - 绕环的角度
            // v: [-1, 1] - 环带的宽度
            u = u * Math.PI * 2;
            v = v * 0.3; // 控制环带宽度
            
            const x = Math.cos(u) * (3 + v * Math.cos(u / 2));
            const y = Math.sin(u) * (3 + v * Math.cos(u / 2));
            const z = v * Math.sin(u / 2);
            
            target.set(x, y, z);
        }

        // 创建参数化几何体
        const geometry = new THREE.ParametricGeometry(mobiusFunction, 100, 20);
        
        // 计算法向量以获得正确的光照
        geometry.computeVertexNormals();

        // 创建渐变材质
        const material = new THREE.MeshPhongMaterial({
            color: 0x4a90e2,
            shininess: 100,
            transparent: true,
            opacity: 0.9,
            side: THREE.DoubleSide, // 双面渲染以显示莫比乌斯环的特性
        });

        // 添加顶点颜色来创建渐变效果
        this.addGradientColors(geometry);
        material.vertexColors = true;

        // 创建网格
        this.mobiusRing = new THREE.Mesh(geometry, material);
        this.mobiusRing.castShadow = true;
        this.mobiusRing.receiveShadow = true;
        
        this.scene.add(this.mobiusRing);
    }

    /**
     * 为几何体添加渐变颜色
     */
    addGradientColors(geometry) {
        const colors = [];
        const positions = geometry.attributes.position;
        
        for (let i = 0; i < positions.count; i++) {
            const vertex = new THREE.Vector3();
            vertex.fromBufferAttribute(positions, i);
            
            // 基于z坐标创建颜色渐变
            const hue = (vertex.z + 0.5) * 0.6 + 0.1; // 从蓝色到紫色
            const saturation = 0.8;
            const lightness = 0.6;
            
            const color = new THREE.Color().setHSL(hue, saturation, lightness);
            colors.push(color.r, color.g, color.b);
        }
        
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
    }

    /**
     * 创建光照系统
     */
    createLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // 补充光源
        const fillLight = new THREE.DirectionalLight(0x87ceeb, 0.3);
        fillLight.position.set(-5, -5, -5);
        this.scene.add(fillLight);

        // 点光源（动态效果）
        const pointLight = new THREE.PointLight(0xff6b6b, 0.5, 20);
        pointLight.position.set(0, 5, 0);
        this.scene.add(pointLight);
    }

    /**
     * 创建轨迹演示小球
     */
    createParticle() {
        const geometry = new THREE.SphereGeometry(0.1, 16, 16);
        const material = new THREE.MeshPhongMaterial({ 
            color: 0xff4444,
            emissive: 0x330000,
            shininess: 100
        });
        
        this.particle = new THREE.Mesh(geometry, material);
        this.particle.castShadow = true;
        this.scene.add(this.particle);
    }

    /**
     * 更新小球位置沿莫比乌斯环轨迹
     */
    updateParticlePosition() {
        if (!this.showParticle || !this.particle) return;
        
        // 使用莫比乌斯环参数方程计算小球位置
        const u = this.particleT;
        const v = 0; // 沿中心线移动
        
        const angle = u * Math.PI * 2;
        const x = Math.cos(angle) * (3 + v * Math.cos(angle / 2));
        const y = Math.sin(angle) * (3 + v * Math.cos(angle / 2));
        const z = v * Math.sin(angle / 2);
        
        this.particle.position.set(x, y, z);
        this.particle.visible = this.showParticle;
        
        // 更新参数，实现连续运动
        this.particleT += 0.01;
        if (this.particleT > 2) {
            this.particleT = 0;
        }
    }

    /**
     * 添加辅助线（开发调试用）
     */
    addHelpers() {
        // 坐标轴辅助线
        const axesHelper = new THREE.AxesHelper(5);
        this.scene.add(axesHelper);
        
        // 网格辅助线
        const gridHelper = new THREE.GridHelper(10, 10);
        this.scene.add(gridHelper);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize(), false);
        
        // 控制面板事件
        const autoRotateCheckbox = document.getElementById('auto-rotate');
        const showParticleCheckbox = document.getElementById('show-particle');
        const rotationSpeedSlider = document.getElementById('rotation-speed');
        const resetViewButton = document.getElementById('reset-view');
        
        autoRotateCheckbox?.addEventListener('change', (e) => {
            this.autoRotate = e.target.checked;
            this.controls.autoRotate = this.autoRotate;
        });
        
        showParticleCheckbox?.addEventListener('change', (e) => {
            this.showParticle = e.target.checked;
            if (this.particle) {
                this.particle.visible = this.showParticle;
            }
        });
        
        rotationSpeedSlider?.addEventListener('input', (e) => {
            this.rotationSpeed = parseFloat(e.target.value);
            this.controls.autoRotateSpeed = this.rotationSpeed * 100;
        });
        
        resetViewButton?.addEventListener('click', () => {
            this.resetView();
        });
        
        // 初始化控制器自动旋转
        this.controls.autoRotate = this.autoRotate;
        this.controls.autoRotateSpeed = this.rotationSpeed * 100;
    }

    /**
     * 重置视角
     */
    resetView() {
        this.camera.position.set(0, 0, 8);
        this.controls.reset();
    }

    /**
     * 窗口大小调整处理
     */
    onWindowResize() {
        const container = document.getElementById('scene-container');
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    /**
     * 动画循环
     */
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());
        
        // 更新控制器
        this.controls.update();
        
        // 更新小球位置
        this.updateParticlePosition();
        
        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }

    /**
     * 销毁资源
     */
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // 清理几何体和材质
        this.scene.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });
        
        // 清理渲染器
        this.renderer.dispose();
    }
}

// 错误处理和资源加载
function initializeVisualization() {
    try {
        // 检查Three.js是否加载
        if (typeof THREE === 'undefined') {
            throw new Error('Three.js库未能正确加载，请检查网络连接');
        }
        
        // 等待OrbitControls加载（可能需要时间）
        checkOrbitControlsAndInit();
        
    } catch (error) {
        console.error('初始化莫比乌斯环可视化时发生错误:', error);
        showErrorMessage(error.message);
    }
}

function checkOrbitControlsAndInit(retryCount = 0) {
    const maxRetries = 10; // 最大重试次数
    const retryDelay = 500; // 重试间隔(毫秒)
    
    if (typeof THREE.OrbitControls !== 'undefined') {
        // OrbitControls已加载，开始初始化
        try {
            window.mobiusVisualizer = new MobiusRingVisualizer();
            hideLoadingMessage();
        } catch (error) {
            console.error('创建莫比乌斯环可视化实例时发生错误:', error);
            showErrorMessage('初始化3D场景失败: ' + error.message);
        }
    } else if (retryCount < maxRetries) {
        // 还在等待OrbitControls加载
        showLoadingMessage(`正在加载轨道控制器... (${retryCount + 1}/${maxRetries})`);
        setTimeout(() => {
            checkOrbitControlsAndInit(retryCount + 1);
        }, retryDelay);
    } else {
        // 重试次数用完，显示错误
        const errorMsg = 'OrbitControls加载超时。这可能是由于网络问题或CDN服务异常导致的。';
        console.error(errorMsg);
        showErrorMessage(errorMsg, true);
    }
}

function showLoadingMessage(message) {
    const container = document.getElementById('scene-container');
    container.innerHTML = `
        <div class="loading">
            <div class="loading-spinner"></div>
            ${message}
        </div>
    `;
}

function showErrorMessage(message, showRetryButton = false) {
    const container = document.getElementById('scene-container');
    const retryButtonHtml = showRetryButton ? 
        '<br><br><button onclick="location.reload()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">重新加载页面</button>' : '';
    
    container.innerHTML = `
        <div class="loading" style="color: #e74c3c;">
            ⚠️ 加载失败
            <br><br>
            ${message}
            ${retryButtonHtml}
            <br><br>
            <div style="font-size: 0.9em; color: #666; max-width: 400px; line-height: 1.5;">
                <strong>可能的解决方案：</strong><br>
                1. 检查网络连接<br>
                2. 刷新页面重试<br>
                3. 尝试使用其他浏览器<br>
                4. 检查是否启用了广告拦截器
            </div>
        </div>
    `;
}

function hideLoadingMessage() {
    // 移除加载消息，显示正常的canvas
    const container = document.getElementById('scene-container');
    const existingCanvas = container.querySelector('#mobius-canvas');
    if (!existingCanvas) {
        // 如果canvas被移除了，重新创建
        const canvas = document.createElement('canvas');
        canvas.id = 'mobius-canvas';
        container.innerHTML = '';
        container.appendChild(canvas);
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeVisualization);
} else {
    initializeVisualization();
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.mobiusVisualizer) {
        window.mobiusVisualizer.destroy();
    }
});

// 导出给其他脚本使用（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobiusRingVisualizer;
}
